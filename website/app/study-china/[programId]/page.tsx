import type React from "react"
import { notFound } from "next/navigation"
import ChinaProgramDetailHero from "@/components/ChinaProgramDetailHero"
import ChinaProgramDetail from "@/components/ChinaProgramDetail"
import Footer from "@/components/Footer"

// 模拟中国游学项目数据
const chinaProgramsData = {
  "beijing-cultural-immersion": {
    programId: "beijing-cultural-immersion",
    title: "中国创新之道：文化根基到数字未来",
    description:
      "本项目是一个为期两周的深度体验式学习项目，融合讲座课堂、企业参访、文化工作坊与团队项目，以探索中国如何从传统文化根基发展出强劲的数字创新力量。项目面向全球学生，结合SWUFE的金融科技与创新研究优势，深入成都本地创新生态，带来沉浸式的中国创新实践体验。",
    type: ["文化体验", "学术拓展"],
    city: "成都",
    gradeLevel: ["高中", "大学"],
    duration: "2周；14天",
    backgroundImage: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d?q=80&w=2070&fit=crop",
    sessions: ["2025年7月1日 - 7月14日", "2025年7月15日 - 7月28日", "2025年8月1日 - 8月14日"],
    deadline: "2025年6月15日",
    images: [
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/screencapture-edgoing-university-2-2025-05-27-15_53_51-slzI2PZ0fTPxiT4eUj7WBT1WSXuobY.png",
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?q=80&w=2070&fit=crop",
    ],
  },
  "shanghai-business-innovation": {
    programId: "shanghai-business-innovation",
    title: "上海商业创新与科技探索",
    description: "深入上海国际金融中心，参访知名企业，了解中国商业模式创新，体验前沿科技发展。",
    type: ["商务与艺术类", "STEM与科学创新"],
    city: "上海",
    gradeLevel: ["高中", "大学"],
    duration: "2周；14天",
    backgroundImage: "https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?q=80&w=2070&fit=crop",
    sessions: ["2025年7月5日 - 7月18日", "2025年8月5日 - 8月18日"],
    deadline: "2025年6月20日",
    images: [
      "https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
    ],
  },
}

interface ChinaProgramDetailPageProps {
  params: {
    programId: string
  }
}

const ChinaProgramDetailPage: React.FC<ChinaProgramDetailPageProps> = ({ params }) => {
  const { programId } = params
  const program = chinaProgramsData[programId as keyof typeof chinaProgramsData]

  if (!program) {
    notFound()
  }

  return (
    <div className="min-h-screen flex flex-col">
      <ChinaProgramDetailHero title={program.title} city={program.city} backgroundImage={program.backgroundImage} />
      <div className="flex-grow bg-gray-50">
        <ChinaProgramDetail program={program} />
      </div>
      <Footer />
    </div>
  )
}

export default ChinaProgramDetailPage
