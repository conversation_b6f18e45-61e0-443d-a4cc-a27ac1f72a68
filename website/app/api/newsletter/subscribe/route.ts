import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { email, name, language = 'zh' } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingSubscription = await prisma.newsletter.findUnique({
      where: { email },
    })

    if (existingSubscription) {
      if (existingSubscription.isActive) {
        return NextResponse.json(
          { error: 'Email is already subscribed' },
          { status: 400 }
        )
      } else {
        // Reactivate subscription
        const updated = await prisma.newsletter.update({
          where: { email },
          data: {
            isActive: true,
            name,
            language,
            unsubscribedAt: null,
          },
        })
        return NextResponse.json({ 
          message: 'Successfully resubscribed!',
          subscription: updated 
        })
      }
    }

    // Create new subscription
    const subscription = await prisma.newsletter.create({
      data: {
        email,
        name,
        language,
        isActive: true,
      },
    })

    return NextResponse.json({ 
      message: 'Successfully subscribed!',
      subscription 
    }, { status: 201 })
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
