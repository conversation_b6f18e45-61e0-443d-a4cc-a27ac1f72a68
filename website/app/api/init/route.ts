import { NextResponse } from 'next/server'
import { createDefaultAdmin } from '@/lib/auth'

export async function POST() {
  try {
    await createDefaultAdmin()
    return NextResponse.json({ message: 'Initialization completed' })
  } catch (error) {
    console.error('Initialization error:', error)
    return NextResponse.json(
      { error: 'Initialization failed' },
      { status: 500 }
    )
  }
}
