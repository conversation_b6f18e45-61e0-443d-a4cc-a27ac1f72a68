import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'
    const limit = parseInt(searchParams.get('limit') || '10')

    // Get published testimonials
    const testimonials = await prisma.testimonial.findMany({
      where: {
        status: 'PUBLISHED',
        language,
      },
      include: {
        image: {
          select: {
            id: true,
            url: true,
            alt: true,
          },
        },
        translations: {
          where: {
            language,
          },
        },
      },
      orderBy: [
        { order: 'asc' },
        { publishedAt: 'desc' },
      ],
      take: limit,
    })

    // Transform data to include translations
    const transformedTestimonials = testimonials.map(testimonial => {
      const translation = testimonial.translations[0]
      return {
        id: testimonial.id,
        content: translation?.content || testimonial.content,
        author: translation?.author || testimonial.author,
        role: translation?.role || testimonial.role,
        program: translation?.program || testimonial.program,
        image: testimonial.image,
        order: testimonial.order,
        publishedAt: testimonial.publishedAt,
      }
    })

    return NextResponse.json({ testimonials: transformedTestimonials })
  } catch (error) {
    console.error('Get testimonials error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
