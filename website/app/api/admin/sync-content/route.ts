import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/middleware'

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAdmin(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    // 从现有的 i18n 配置中同步内容
    const i18nContent = {
      zh: {
        // 网站基本信息
        site_title: 'EdGoing - 探索·学习·成长',
        site_description: '专业的国际教育游学服务平台，为学生提供优质的海外学习体验',
        site_keywords: '国际教育,游学,留学,EdGoing,海外学习',
        
        // 导航栏
        nav_home: '首页',
        nav_about: '关于我们',
        nav_programs: '项目',
        nav_contact: '联系我们',
        
        // 首页英雄区域
        hero_title: '探索·学习·成长',
        hero_subtitle: '您的终身学习之旅等待着您',
        hero_explore: '探索',
        hero_learn: '学习',
        hero_grow: '成长',
        hero_button_text: '开始探索',
        
        // 为什么选择EdGoing
        why_choose_title: '为什么选择',
        why_choose_subtitle: '以变革性、安全和个性化的旅程激发全球思维，获得全球信赖',
        why_choose_expertise_title: '专业知识与经验',
        why_choose_expertise_description: 'EdGoing 植根于全球专业知识，致力于严谨的研究，精心打造最高品质、变革性的教育项目，激励学员在课堂之外学习。',
        why_choose_global_title: '全球视野',
        why_choose_global_description: '通过战略性的全球合作伙伴关系，EdGoing 创造了真实的文化交流，使学员成为几乎所有识广、富有同理心且具有全球视野的未来领导者。',
        why_choose_safety_title: '安全与个性化承诺',
        why_choose_safety_description: 'EdGoing 设计安全、高品质且个性化的旅程，帮助学员掌握终身技能并获得变革性的全球视野。',
        
        // 学员支持与安全
        student_support_title: '学员支持与安全',
        student_support_subtitle: '我们优先考虑您的健康，在您的教育旅程中全天候提供全面的支持服务。',
        support_leadership_title: '经验丰富的领导团队',
        support_leadership_description: '我们的专业团队具备丰富经验，为您的学习之旅提供专业指导和全面支持。',
        support_education_title: '高质量教育项目',
        support_education_description: '由专家主导的课程，与顶级合作伙伴合作，提供卓越的教育体验。',
        support_accommodation_title: '安全住宿和健康餐饮',
        support_accommodation_description: '安全的住宿环境，以及适合各种饮食需求的营养餐饮，以确保您的健康。',
        support_247_title: '全天候支持',
        support_247_description: '全天候服务，为学员提供持续支持。',
        support_cultural_title: '沉浸式文化体验',
        support_cultural_description: '互动活动，融入丰富的游览和当地参与，促进深度学习。',
        support_academic_title: '受认可的学术卓越',
        support_academic_description: '首屈一指的高等学府表现和来自大学申请的课程。',
        
        // 学员故事
        testimonials_title: '学员故事',
        testimonials_subtitle: '听听我们学员分享他们的学习经历和成长故事。',
        testimonial_1_content: '我在新加坡的游学经历非常棒，不仅让我学到了很多科学知识，还让我体验了不同的文化。老师们都很专业，课程设计得很有趣，让我对科学产生了更大的兴趣。这次旅行真的改变了我对世界的看法。',
        testimonial_1_author: '张文慧',
        testimonial_1_role: '高中生',
        testimonial_1_program: '新加坡科学营',
        testimonial_2_content: '参加EdGoing的项目让我的孩子变得更加自信和独立。她不仅提高了英语水平，还学会了如何与来自不同文化背景的同学相处。这是一次非常值得的投资。',
        testimonial_2_author: '李明',
        testimonial_2_role: '学生家长',
        testimonial_2_program: '国际文化交流项目',
        testimonial_3_content: '通过EdGoing的STEM项目，我对编程和机器人技术产生了浓厚的兴趣。导师们都很专业，教学方式很有趣，让我在玩中学到了很多知识。',
        testimonial_3_author: '王小明',
        testimonial_3_role: '初中生',
        testimonial_3_program: 'STEM创新营',
        
        // 邮件订阅
        newsletter_title: '订阅我们的最新消息',
        newsletter_subtitle: '订阅我们的学习通讯，获取学习资源和内容，并获得优惠信息。',
        newsletter_placeholder: '输入您的邮箱地址',
        newsletter_button: '订阅',
        
        // 行动号召
        cta_title: '准备开始您的旅程？',
        cta_subtitle: '迈出国际教育冒险的第一步，我们的团队随时为您提供规划完美项目的帮助。',
        cta_button_text: '开始咨询',
        
        // 页脚
        footer_description: '专业的国际教育服务平台，致力于为学生提供优质的海外学习体验。',
        footer_copyright: '2025 EdGoing. 版权所有',
        footer_contact_title: '联系我们',
        footer_contact_address: '中国上海市浦东新区',
        footer_contact_phone: '+86 21 1234 5678',
        footer_contact_email: '<EMAIL>',
        footer_links_title: '快速链接',
        footer_social_title: '关注我们',
        
        // 项目页面
        programs_title: '探索我们的项目',
        programs_subtitle: '通过我们多样化的教育项目探索学习机会的世界',
      },
      en: {
        // 网站基本信息
        site_title: 'EdGoing - Explore·Learn·Grow',
        site_description: 'Professional international education and study tour platform providing quality overseas learning experiences',
        site_keywords: 'international education,study tour,study abroad,EdGoing,overseas learning',
        
        // 导航栏
        nav_home: 'Home',
        nav_about: 'About',
        nav_programs: 'Programs',
        nav_contact: 'Contact',
        
        // 首页英雄区域
        hero_title: 'Explore·Learn·Grow',
        hero_subtitle: 'Your Lifetime Learning Journey Awaits',
        hero_explore: 'Explore',
        hero_learn: 'Learn',
        hero_grow: 'Grow',
        hero_button_text: 'Start Exploring',
        
        // 为什么选择EdGoing
        why_choose_title: 'Why Choose',
        why_choose_subtitle: 'Inspiring global thinking through transformative, safe and personalized journeys, trusted worldwide',
        why_choose_expertise_title: 'Professional Knowledge & Experience',
        why_choose_expertise_description: 'EdGoing is rooted in global expertise, committed to rigorous research, and carefully crafts the highest quality, transformative educational programs that inspire students to learn beyond the classroom.',
        why_choose_global_title: 'Global Vision',
        why_choose_global_description: 'Through strategic global partnerships, EdGoing creates authentic cultural exchanges, enabling students to become well-informed, empathetic future leaders with a global perspective.',
        why_choose_safety_title: 'Safety & Personalization Commitment',
        why_choose_safety_description: 'EdGoing designs safe, high-quality and personalized journeys to help students master lifelong skills and gain transformative global perspectives.',
        
        // 学员支持与安全
        student_support_title: 'Student Support and Safety',
        student_support_subtitle: 'We prioritize your health and safety first, providing comprehensive support services throughout your educational journey.',
        support_leadership_title: 'Experienced Leadership Team',
        support_leadership_description: 'Our experienced team provides professional guidance and comprehensive support throughout your journey.',
        support_education_title: 'High-Quality Education Programs',
        support_education_description: 'Carefully designed programs with top partner institutions to provide excellent educational experiences.',
        support_accommodation_title: 'Safe Accommodation and Healthy Dining',
        support_accommodation_description: 'Safe living environments and nutritious meals to ensure your health and well-being.',
        support_247_title: '24/7 Support',
        support_247_description: 'Round-the-clock support services to provide continuous assistance for students.',
        support_cultural_title: 'Immersive Cultural Experience',
        support_cultural_description: 'Interactive activities and rich travel experiences to promote deep cultural learning.',
        support_academic_title: 'Recognized Academic Excellence',
        support_academic_description: 'Courses recognized by top universities, enhancing your academic credentials.',
        
        // 学员故事
        testimonials_title: 'Student Stories',
        testimonials_subtitle: 'Listen to our students share their learning experiences and growth stories.',
        testimonial_1_content: 'My study tour experience in Singapore was amazing. Not only did I learn a lot of scientific knowledge, but I also experienced different cultures. The teachers were very professional and the courses were designed to be very interesting, which made me more interested in science. This trip really changed my view of the world.',
        testimonial_1_author: 'Zhang Wenhui',
        testimonial_1_role: 'High School Student',
        testimonial_1_program: 'Singapore Science Camp',
        testimonial_2_content: 'Participating in EdGoing\'s program made my child more confident and independent. She not only improved her English level, but also learned how to get along with classmates from different cultural backgrounds. This was a very worthwhile investment.',
        testimonial_2_author: 'Li Ming',
        testimonial_2_role: 'Parent',
        testimonial_2_program: 'International Cultural Exchange Program',
        testimonial_3_content: 'Through EdGoing\'s STEM program, I developed a strong interest in programming and robotics. The mentors were very professional and the teaching methods were very interesting, allowing me to learn a lot while playing.',
        testimonial_3_author: 'Wang Xiaoming',
        testimonial_3_role: 'Middle School Student',
        testimonial_3_program: 'STEM Innovation Camp',
        
        // 邮件订阅
        newsletter_title: 'Subscribe to Our Latest News',
        newsletter_subtitle: 'Subscribe to our learning newsletter to get learning resources and content, and receive special offers.',
        newsletter_placeholder: 'Enter your email address',
        newsletter_button: 'Subscribe',
        
        // 行动号召
        cta_title: 'Ready to Start Your Journey?',
        cta_subtitle: 'Take the first step in your international education adventure. Our team is ready to help you plan the perfect program.',
        cta_button_text: 'Start Consultation',
        
        // 页脚
        footer_description: 'Professional international education platform dedicated to providing students with quality overseas learning experiences.',
        footer_copyright: '2025 EdGoing. All rights reserved',
        footer_contact_title: 'Contact Us',
        footer_contact_address: 'Pudong New Area, Shanghai, China',
        footer_contact_phone: '+86 21 1234 5678',
        footer_contact_email: '<EMAIL>',
        footer_links_title: 'Quick Links',
        footer_social_title: 'Follow Us',
        
        // 项目页面
        programs_title: 'Explore Our Programs',
        programs_subtitle: 'Explore the world of learning opportunities through our diverse educational programs',
      }
    }

    let syncedCount = 0

    // 同步中文和英文内容
    for (const [language, content] of Object.entries(i18nContent)) {
      for (const [key, value] of Object.entries(content)) {
        await prisma.setting.upsert({
          where: { key },
          update: { 
            value: value as string, 
            language,
            type: 'TEXT'
          },
          create: {
            key,
            value: value as string,
            type: 'TEXT',
            language,
          },
        })
        syncedCount++
      }
    }

    return NextResponse.json({ 
      message: `Successfully synced ${syncedCount} content items`,
      syncedCount 
    })
  } catch (error) {
    console.error('Sync content error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
