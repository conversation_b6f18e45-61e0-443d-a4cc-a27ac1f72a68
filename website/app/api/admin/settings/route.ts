import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireEditor } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'

    const settings = await prisma.setting.findMany({
      where: { language },
      include: {
        translations: true,
      },
      orderBy: { key: 'asc' },
    })

    return NextResponse.json({ settings })
  } catch (error) {
    console.error('Get settings error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const data = await request.json()
    const { key, value, type = 'TEXT', language = 'zh' } = data

    // Check if setting already exists
    const existingSetting = await prisma.setting.findUnique({
      where: { key },
    })

    if (existingSetting) {
      return NextResponse.json(
        { error: 'Setting key already exists' },
        { status: 400 }
      )
    }

    const setting = await prisma.setting.create({
      data: {
        key,
        value,
        type,
        language,
      },
    })

    return NextResponse.json(setting, { status: 201 })
  } catch (error) {
    console.error('Create setting error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const data = await request.json()
    const { key, value, type, language = 'zh' } = data

    const setting = await prisma.setting.update({
      where: { key },
      data: {
        value,
        type,
        language,
      },
    })

    return NextResponse.json(setting)
  } catch (error) {
    console.error('Update setting error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
