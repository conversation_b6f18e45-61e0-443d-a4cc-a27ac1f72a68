import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireEditor } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const language = searchParams.get('language') || 'zh'
    const status = searchParams.get('status')

    const where: any = { language }
    if (status) {
      where.status = status
    }

    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        include: {
          image: {
            select: {
              id: true,
              url: true,
              alt: true,
              filename: true,
            },
          },
          authorUser: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
          translations: true,
        },
        orderBy: [
          { order: 'asc' },
          { createdAt: 'desc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.testimonial.count({ where }),
    ])

    return NextResponse.json({
      testimonials,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Get testimonials error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const data = await request.json()
    const {
      content,
      author,
      role,
      program,
      status = 'PUBLISHED',
      language = 'zh',
      imageId,
      order = 0,
      translations = [],
    } = data

    // Validate required fields
    if (!content || !author || !role || !program) {
      return NextResponse.json(
        { error: 'Content, author, role, and program are required' },
        { status: 400 }
      )
    }

    // Create testimonial
    const testimonial = await prisma.testimonial.create({
      data: {
        content,
        author,
        role,
        program,
        status,
        language,
        imageId,
        order,
        authorId: authResult.user.id,
        publishedAt: status === 'PUBLISHED' ? new Date() : null,
        translations: {
          create: translations.map((t: any) => ({
            language: t.language,
            content: t.content,
            author: t.author,
            role: t.role,
            program: t.program,
          })),
        },
      },
      include: {
        image: {
          select: {
            id: true,
            url: true,
            alt: true,
            filename: true,
          },
        },
        translations: true,
      },
    })

    return NextResponse.json({ testimonial })
  } catch (error) {
    console.error('Create testimonial error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
