import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireEditor } from '@/lib/middleware'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const testimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
      include: {
        image: {
          select: {
            id: true,
            url: true,
            alt: true,
            filename: true,
          },
        },
        authorUser: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        translations: true,
      },
    })

    if (!testimonial) {
      return NextResponse.json(
        { error: 'Testimonial not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ testimonial })
  } catch (error) {
    console.error('Get testimonial error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const data = await request.json()
    const {
      content,
      author,
      role,
      program,
      status,
      imageId,
      order,
      translations = [],
    } = data

    // Validate required fields
    if (!content || !author || !role || !program) {
      return NextResponse.json(
        { error: 'Content, author, role, and program are required' },
        { status: 400 }
      )
    }

    // Check if testimonial exists
    const existingTestimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
    })

    if (!existingTestimonial) {
      return NextResponse.json(
        { error: 'Testimonial not found' },
        { status: 404 }
      )
    }

    // Update testimonial
    const testimonial = await prisma.testimonial.update({
      where: { id: params.id },
      data: {
        content,
        author,
        role,
        program,
        status,
        imageId,
        order,
        publishedAt: status === 'PUBLISHED' && !existingTestimonial.publishedAt 
          ? new Date() 
          : existingTestimonial.publishedAt,
      },
      include: {
        image: {
          select: {
            id: true,
            url: true,
            alt: true,
            filename: true,
          },
        },
        translations: true,
      },
    })

    // Update translations
    if (translations.length > 0) {
      // Delete existing translations
      await prisma.testimonialTranslation.deleteMany({
        where: { testimonialId: params.id },
      })

      // Create new translations
      await prisma.testimonialTranslation.createMany({
        data: translations.map((t: any) => ({
          testimonialId: params.id,
          language: t.language,
          content: t.content,
          author: t.author,
          role: t.role,
          program: t.program,
        })),
      })
    }

    return NextResponse.json({ testimonial })
  } catch (error) {
    console.error('Update testimonial error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireEditor(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    // Check if testimonial exists
    const testimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
    })

    if (!testimonial) {
      return NextResponse.json(
        { error: 'Testimonial not found' },
        { status: 404 }
      )
    }

    // Delete testimonial (translations will be deleted automatically due to cascade)
    await prisma.testimonial.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete testimonial error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
