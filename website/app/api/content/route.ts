import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'

    const settings = await prisma.setting.findMany({
      where: { language },
      select: {
        key: true,
        value: true,
      },
    })

    // Convert to key-value object
    const content: Record<string, string> = {}
    settings.forEach(setting => {
      content[setting.key] = setting.value
    })

    return NextResponse.json({ content })
  } catch (error) {
    console.error('Get content error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
