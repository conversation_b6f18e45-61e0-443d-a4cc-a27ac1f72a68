import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const language = searchParams.get('language') || 'zh'
    const country = searchParams.get('country')
    const status = searchParams.get('status') || 'PUBLISHED'

    const where: any = {
      status,
      language,
    }

    if (country) {
      where.country = country
    }

    const [programs, total] = await Promise.all([
      prisma.program.findMany({
        where,
        select: {
          id: true,
          title: true,
          slug: true,
          description: true,
          country: true,
          city: true,
          duration: true,
          price: true,
          currency: true,
          maxStudents: true,
          minAge: true,
          maxAge: true,
          startDate: true,
          endDate: true,
          deadline: true,
          featuredImage: true,
          publishedAt: true,
          _count: {
            select: {
              applications: true,
            }
          }
        },
        orderBy: { publishedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.program.count({ where }),
    ])

    return NextResponse.json({
      programs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Get programs error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
