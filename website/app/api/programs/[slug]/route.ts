import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'

    const program = await prisma.program.findFirst({
      where: {
        slug,
        status: 'PUBLISHED',
      },
      include: {
        author: {
          select: {
            name: true,
            username: true,
          }
        },
        translations: {
          where: {
            language,
          }
        },
        _count: {
          select: {
            applications: true,
          }
        }
      },
    })

    if (!program) {
      return NextResponse.json(
        { error: 'Program not found' },
        { status: 404 }
      )
    }

    // Parse JSON fields
    const processedProgram = {
      ...program,
      gallery: program.gallery ? JSON.parse(program.gallery) : [],
      highlights: program.highlights ? JSON.parse(program.highlights) : [],
      academics: program.academics ? JSON.parse(program.academics) : [],
      itinerary: program.itinerary ? JSON.parse(program.itinerary) : [],
      requirements: program.requirements ? JSON.parse(program.requirements) : [],
      materials: program.materials ? JSON.parse(program.materials) : [],
    }

    return NextResponse.json({ program: processedProgram })
  } catch (error) {
    console.error('Get program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
