import type React from "react"
import ProgramDetailHero from "@/components/ProgramDetailHero"
import ProgramDetail from "@/components/ProgramDetail"
import Footer from "@/components/Footer"

const program = {
  title: "【创新未来】加州伯克利大学STEM课程",
  type: ["Academic Study", "Cultural Experience"],
  location: "USA",
  gradeLevel: ["Middle School", "High School"],
  backgroundImage: "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
}

const ProgramDetailPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <ProgramDetailHero
        title={program.title}
        type={program.type}
        location={program.location}
        gradeLevel={program.gradeLevel}
        backgroundImage={program.backgroundImage}
      />
      <div className="flex-grow bg-gray-50">
        <ProgramDetail programId="USIV-2023-002" />
      </div>
      <Footer />
    </div>
  )
}

export default ProgramDetailPage
