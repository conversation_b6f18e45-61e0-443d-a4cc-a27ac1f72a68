import type React from "react"
import InternationalProgramDetailHero from "@/components/InternationalProgramDetailHero"
import InternationalProgramDetail from "@/components/InternationalProgramDetail"
import Footer from "@/components/Footer"

const programData = {
  programId: "singapore-science-village",
  titleKey: "programs.singaporeScience.title",
  titleFallback: "2025年阿雅科村国际英语暑期学校",
  descriptionKey: "programs.singaporeScience.description",
  descriptionFallback:
    "我们的国际英语暑期学校为期14天的学习营，设计，提供全面且自然深度的体验，帮助学生提升英语水平并体验新加坡文化。",
  typeKeys: ["programs.types.language", "programs.types.cultural"],
  typeFallbacks: ["语言强化", "国际交流"],
  countryKey: "countries.singapore",
  countryFallback: "新加坡",
  gradeLevelKeys: ["grades.middleSchool", "grades.highSchool"],
  gradeLevelFallbacks: ["中学", "高中"],
  sessionKeys: ["programs.singaporeScience.sessions.session1", "programs.singaporeScience.sessions.session2"],
  sessionFallbacks: ["2025年7月5日 - 7月18日", "2025年8月5日 - 8月18日"],
  deadlineKey: "programs.singaporeScience.deadline",
  deadlineFallback: "2025年6月20日",
  durationKey: "programs.singaporeScience.duration",
  durationFallback: "2周；14天",
  backgroundImage: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
  images: [
    "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1517486808906-6ca8b3f04846?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&fit=crop",
  ],
  highlightsKeys: [
    "programs.singaporeScience.highlights.highlight1",
    "programs.singaporeScience.highlights.highlight2",
    "programs.singaporeScience.highlights.highlight3",
    "programs.singaporeScience.highlights.highlight4",
    "programs.singaporeScience.highlights.highlight5",
  ],
  highlightsFallbacks: [
    "沉浸式英语学习环境",
    "体验新加坡多元文化",
    "专业英语教师指导",
    "丰富的课外活动",
    "获得国际认证证书",
  ],
  academicsKeys: [
    "programs.singaporeScience.academics.academic1",
    "programs.singaporeScience.academics.academic2",
    "programs.singaporeScience.academics.academic3",
    "programs.singaporeScience.academics.academic4",
    "programs.singaporeScience.academics.academic5",
  ],
  academicsFallbacks: ["英语听说读写综合训练", "学术英语写作技巧", "跨文化交流技能", "演讲与表达能力", "英语文学欣赏"],
  itineraryKeys: [
    "programs.singaporeScience.itinerary.item1",
    "programs.singaporeScience.itinerary.item2",
    "programs.singaporeScience.itinerary.item3",
    "programs.singaporeScience.itinerary.item4",
    "programs.singaporeScience.itinerary.item5",
    "programs.singaporeScience.itinerary.item6",
    "programs.singaporeScience.itinerary.item7",
    "programs.singaporeScience.itinerary.item8",
  ],
  itineraryFallbacks: [
    "英语基础课程",
    "文化体验活动",
    "城市探索之旅",
    "小组讨论练习",
    "实践项目展示",
    "当地学校参访",
    "结业演讲准备",
    "证书颁发仪式",
  ],
  admissionKeys: [
    "programs.singaporeScience.admission.req1",
    "programs.singaporeScience.admission.req2",
    "programs.singaporeScience.admission.req3",
    "programs.singaporeScience.admission.req4",
    "programs.singaporeScience.admission.material1",
    "programs.singaporeScience.admission.material2",
    "programs.singaporeScience.admission.material3",
    "programs.singaporeScience.admission.material4",
    "programs.singaporeScience.admission.material5",
  ],
  admissionFallbacks: [
    "年龄：12-18岁",
    "学历：初中在读或以上",
    "语言：具备基础英语水平",
    "积极参与学习活动",
    "申请表",
    "英语水平测试",
    "学术成绩单",
    "推荐信（可选）",
    "护照复印件",
  ],
}

const SingaporeScienceVillagePage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <InternationalProgramDetailHero
        titleKey={programData.titleKey}
        titleFallback={programData.titleFallback}
        countryKey={programData.countryKey}
        countryFallback={programData.countryFallback}
        backgroundImage={programData.backgroundImage}
      />
      <div className="flex-grow bg-gray-50">
        <InternationalProgramDetail program={programData} />
      </div>
      <Footer />
    </div>
  )
}

export default SingaporeScienceVillagePage
