import type React from "react"
import ProgramDetailHero from "@/components/ProgramDetailHero"
import ProgramDetail from "@/components/ProgramDetail"
import Footer from "@/components/Footer"

const program = {
  title: "Youth Art Advanced Program",
  type: ["Academic Study", "Cultural Experience"],
  location: "USA",
  gradeLevel: ["Middle School", "High School"],
  backgroundImage: "https://images.unsplash.com/photo-1513364776144-60967b0f800f?q=80&w=2071&fit=crop",
}

const ProgramDetailPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <ProgramDetailHero
        title={program.title}
        type={program.type}
        location={program.location}
        gradeLevel={program.gradeLevel}
        backgroundImage={program.backgroundImage}
      />
      <div className="flex-grow bg-gray-50">
        <ProgramDetail programId="USART-2023-001" />
      </div>
      <Footer />
    </div>
  )
}

export default ProgramDetailPage
