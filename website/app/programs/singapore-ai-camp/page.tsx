interface Program {
  programId: string
  titleKey: string
  titleFallback: string
  descriptionKey: string
  descriptionFallback: string
  typeKeys: string[]
  typeFallbacks: string[]
  countryKey: string
  countryFallback: string
  gradeLevelKeys: string[]
  gradeLevelFallbacks: string[]
  sessionKeys: string[]
  sessionFallbacks: string[]
  deadlineKey: string
  deadlineFallback: string
  durationKey: string
  durationFallback: string
  // Add other fields as needed
}

const program: Program = {
  programId: "singapore-ai-camp",
  titleKey: "programs.singaporeAI.title",
  titleFallback: "新加坡AI创新营",
  descriptionKey: "programs.singaporeAI.description",
  descriptionFallback: "探索人工智能的前沿技术，在新加坡这个科技创新中心体验最新的AI应用和研发环境。",
  typeKeys: ["programs.types.innovation", "programs.types.stem"],
  typeFallbacks: ["创新营理", "STEM与科学创新"],
  countryKey: "countries.singapore",
  countryFallback: "新加坡",
  gradeLevelKeys: ["grades.highSchool", "grades.college"],
  gradeLevelFallbacks: ["高中", "大学"],
  sessionKeys: [
    "programs.singaporeAI.sessions.session1",
    "programs.singaporeAI.sessions.session2",
    "programs.singaporeAI.sessions.session3",
  ],
  sessionFallbacks: ["2025年7月10日 - 7月16日", "2025年8月10日 - 8月16日", "2025年8月24日 - 8月30日"],
  deadlineKey: "programs.singaporeAI.deadline",
  deadlineFallback: "2025年6月25日",
  durationKey: "programs.singaporeAI.duration",
  durationFallback: "1周；7天",
  // ... 其他字段保持不变
}

const SingaporeAICampPage = () => {
  return (
    <div>
      <h1>{program.titleFallback}</h1>
      <p>{program.descriptionFallback}</p>
      {/* Add more content based on the program data */}
    </div>
  )
}

export default SingaporeAICampPage
