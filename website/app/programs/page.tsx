"use client"

import { useEffect, useRef } from "react"
import { useSearchParams } from "next/navigation"
import PageHero from "@/components/PageHero"
import ProgramSearch from "@/components/ProgramSearch"
import ProgramList from "@/components/ProgramList"
import CallToAction from "@/components/CallToAction"
import Footer from "@/components/Footer"

const ProgramsPage = () => {
  const heroRef = useRef<HTMLDivElement>(null)
  const searchParams = useSearchParams()

  useEffect(() => {
    if (searchParams.get("scroll") === "hero" && heroRef.current) {
      heroRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [searchParams])

  return (
    <div className="min-h-screen flex flex-col">
      <div ref={heroRef}>
        <PageHero
          title="探索我们的项目"
          description="通过我们多样化的教育项目探索学习机会的世界"
          backgroundImage="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop"
        />
      </div>
      <div className="flex-grow bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="lg:w-1/5">
              <ProgramSearch />
            </div>
            <div className="lg:w-4/5">
              <ProgramList />
            </div>
          </div>
        </div>
      </div>
      <CallToAction />
      <Footer />
    </div>
  )
}

export default ProgramsPage
