import type React from "react"
import { notFound } from "next/navigation"
import InternationalProgramDetailHero from "@/components/InternationalProgramDetailHero"
import InternationalProgramDetail from "@/components/InternationalProgramDetail"
import Footer from "@/components/Footer"

// 支持中英文的国际项目数据结构
const programsData = {
  "singapore-english-camp": {
    programId: "singapore-english-camp",
    titleKey: "programs.singaporeEnglish.title",
    titleFallback: "2025年新加坡海峡空英语营",
    descriptionKey: "programs.singaporeEnglish.description",
    descriptionFallback:
      "新加坡海峡空英语营是一个为期7天的沉浸式项目，专为10至15岁的学生设计，该项目旨在提升学生的英语水平。",
    typeKeys: ["programs.types.language", "programs.types.cultural"],
    typeFallbacks: ["语言强化", "国际交流"],
    countryKey: "countries.singapore",
    countryFallback: "新加坡",
    gradeLevelKeys: ["grades.elementary", "grades.middleSchool"],
    gradeLevelFallbacks: ["小学", "中学"],
    sessionKeys: ["programs.singaporeEnglish.sessions.session1", "programs.singaporeEnglish.sessions.session2"],
    sessionFallbacks: ["2025年7月20日 - 7月26日", "2025年8月15日 - 8月21日"],
    deadlineKey: "programs.singaporeEnglish.deadline",
    deadlineFallback: "2025年6月30日",
    durationKey: "programs.singaporeEnglish.duration",
    durationFallback: "1周；7天",
    backgroundImage: "https://images.unsplash.com/photo-1517486808906-6ca8b3f04846?q=80&w=2070&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1517486808906-6ca8b3f04846?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&fit=crop",
    ],
    highlightsKeys: [
      "programs.singaporeEnglish.highlights.highlight1",
      "programs.singaporeEnglish.highlights.highlight2",
      "programs.singaporeEnglish.highlights.highlight3",
      "programs.singaporeEnglish.highlights.highlight4",
      "programs.singaporeEnglish.highlights.highlight5",
    ],
    highlightsFallbacks: [
      "专为青少年设计的英语课程",
      "小班教学，个性化指导",
      "丰富的户外活动和文化体验",
      "安全的学习环境",
      "结业证书认证",
    ],
    academicsKeys: [
      "programs.singaporeEnglish.academics.academic1",
      "programs.singaporeEnglish.academics.academic2",
      "programs.singaporeEnglish.academics.academic3",
      "programs.singaporeEnglish.academics.academic4",
      "programs.singaporeEnglish.academics.academic5",
    ],
    academicsFallbacks: [
      "基础英语语法和词汇",
      "口语表达和发音训练",
      "英语阅读理解",
      "简单英语写作",
      "英语游戏和互动活动",
    ],
    itineraryKeys: [
      "programs.singaporeEnglish.itinerary.item1",
      "programs.singaporeEnglish.itinerary.item2",
      "programs.singaporeEnglish.itinerary.item3",
      "programs.singaporeEnglish.itinerary.item4",
      "programs.singaporeEnglish.itinerary.item5",
      "programs.singaporeEnglish.itinerary.item6",
      "programs.singaporeEnglish.itinerary.item7",
      "programs.singaporeEnglish.itinerary.item8",
    ],
    itineraryFallbacks: [
      "英语基础课程",
      "互动游戏活动",
      "文化探索活动",
      "小组合作项目",
      "户外英语实践",
      "艺术创作活动",
      "成果展示准备",
      "结业庆祝活动",
    ],
    admissionKeys: [
      "programs.singaporeEnglish.admission.req1",
      "programs.singaporeEnglish.admission.req2",
      "programs.singaporeEnglish.admission.req3",
      "programs.singaporeEnglish.admission.req4",
      "programs.singaporeEnglish.admission.material1",
      "programs.singaporeEnglish.admission.material2",
      "programs.singaporeEnglish.admission.material3",
      "programs.singaporeEnglish.admission.material4",
      "programs.singaporeEnglish.admission.material5",
    ],
    admissionFallbacks: [
      "年龄：10-15岁",
      "学历：小学或初中在读",
      "语言：无特殊要求",
      "身体健康，喜欢学习",
      "申请表",
      "家长同意书",
      "学校成绩单",
      "健康证明",
      "护照复印件",
    ],
  },
  "singapore-stem-ai": {
    programId: "singapore-stem-ai",
    titleKey: "programs.singaporeSTEM.title",
    titleFallback: "2025年新加坡STEM与AI营",
    descriptionKey: "programs.singaporeSTEM.description",
    descriptionFallback:
      "新加坡科学与创新STEM夏令营是一个为期7天的沉浸式项目，专为7岁及以上儿童设计，探索科学技术的奥秘。",
    typeKeys: ["programs.types.stem", "programs.types.innovation"],
    typeFallbacks: ["STEM与科学创新", "创新研究"],
    countryKey: "countries.singapore",
    countryFallback: "新加坡",
    gradeLevelKeys: ["grades.elementary", "grades.middleSchool", "grades.highSchool"],
    gradeLevelFallbacks: ["小学", "中学", "高中"],
    sessionKeys: ["programs.singaporeSTEM.sessions.session1", "programs.singaporeSTEM.sessions.session2"],
    sessionFallbacks: ["2025年7月25日 - 7月31日", "2025年8月20日 - 8月26日"],
    deadlineKey: "programs.singaporeSTEM.deadline",
    deadlineFallback: "2025年7月5日",
    durationKey: "programs.singaporeSTEM.duration",
    durationFallback: "1周；7天",
    backgroundImage: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2070&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&fit=crop",
    ],
    highlightsKeys: [
      "programs.singaporeSTEM.highlights.highlight1",
      "programs.singaporeSTEM.highlights.highlight2",
      "programs.singaporeSTEM.highlights.highlight3",
      "programs.singaporeSTEM.highlights.highlight4",
      "programs.singaporeSTEM.highlights.highlight5",
    ],
    highlightsFallbacks: [
      "动手实践的STEM项目",
      "人工智能基础知识学习",
      "科学实验和探索",
      "团队合作和创新思维",
      "专业导师指导",
    ],
    academicsKeys: [
      "programs.singaporeSTEM.academics.academic1",
      "programs.singaporeSTEM.academics.academic2",
      "programs.singaporeSTEM.academics.academic3",
      "programs.singaporeSTEM.academics.academic4",
      "programs.singaporeSTEM.academics.academic5",
    ],
    academicsFallbacks: ["基础编程概念", "机器人制作和编程", "科学实验方法", "数学逻辑思维", "创新设计思维"],
    itineraryKeys: [
      "programs.singaporeSTEM.itinerary.item1",
      "programs.singaporeSTEM.itinerary.item2",
      "programs.singaporeSTEM.itinerary.item3",
      "programs.singaporeSTEM.itinerary.item4",
      "programs.singaporeSTEM.itinerary.item5",
      "programs.singaporeSTEM.itinerary.item6",
      "programs.singaporeSTEM.itinerary.item7",
      "programs.singaporeSTEM.itinerary.item8",
    ],
    itineraryFallbacks: [
      "STEM基础课程",
      "编程入门学习",
      "机器人制作",
      "科学实验室",
      "AI概念学习",
      "团队项目制作",
      "成果展示",
      "结业仪式",
    ],
    admissionKeys: [
      "programs.singaporeSTEM.admission.req1",
      "programs.singaporeSTEM.admission.req2",
      "programs.singaporeSTEM.admission.req3",
      "programs.singaporeSTEM.admission.req4",
      "programs.singaporeSTEM.admission.material1",
      "programs.singaporeSTEM.admission.material2",
      "programs.singaporeSTEM.admission.material3",
      "programs.singaporeSTEM.admission.material4",
      "programs.singaporeSTEM.admission.material5",
    ],
    admissionFallbacks: [
      "年龄：7岁及以上",
      "学历：小学或以上",
      "语言：基础英语理解能力",
      "对科学技术有兴趣",
      "申请表",
      "家长同意书",
      "学校成绩单",
      "健康证明",
      "护照复印件",
    ],
  },
}

interface ProgramDetailPageProps {
  params: {
    programId: string
  }
}

const ProgramDetailPage: React.FC<ProgramDetailPageProps> = ({ params }) => {
  const { programId } = params

  // 添加调试日志
  console.log("Dynamic route - Requested programId:", programId)
  console.log("Dynamic route - Available programs:", Object.keys(programsData))

  const program = programsData[programId as keyof typeof programsData]

  if (!program) {
    console.log("Dynamic route - Program not found for ID:", programId)
    notFound()
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InternationalProgramDetailHero
        titleKey={program.titleKey}
        titleFallback={program.titleFallback}
        countryKey={program.countryKey}
        countryFallback={program.countryFallback}
        backgroundImage={program.backgroundImage}
      />
      <div className="flex-grow bg-gray-50">
        <InternationalProgramDetail program={program} />
      </div>
      <Footer />
    </div>
  )
}

export default ProgramDetailPage
