"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Setting {
  id: string
  key: string
  value: string
  type: string
  language: string
}

export default function ContentManagePage() {
  const [settings, setSettings] = useState<Setting[]>([])
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [language, setLanguage] = useState('zh')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()

  useEffect(() => {
    fetchContent()
  }, [language])

  const fetchContent = async () => {
    try {
      const response = await fetch(`/api/admin/content?language=${language}`)
      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)

        // Convert settings to form data
        const formValues: Record<string, string> = {}
        data.settings.forEach((setting: Setting) => {
          formValues[setting.key] = setting.value
        })
        setFormData(formValues)
      } else if (response.status === 401) {
        router.push('/admin/login')
      } else {
        setError('Failed to fetch content')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/content', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: formData,
          language,
        }),
      })

      if (response.ok) {
        setSuccess('Content updated successfully!')
        setTimeout(() => setSuccess(''), 3000)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to update content')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const getFieldLabel = (key: string) => {
    const labels: Record<string, string> = {
      // 网站基本信息
      site_title: '网站标题',
      site_description: '网站描述',
      site_keywords: '网站关键词',

      // 导航栏
      nav_home: '导航-首页',
      nav_programs: '导航-游学国际',
      nav_study_china: '导航-游学中国',
      nav_about: '导航-关于EdGoing',
      nav_blog: '导航-博客',
      nav_contact: '导航-开始项目',

      // Banner轮播图
      banner_1_title: 'Banner1-标题',
      banner_1_subtitle: 'Banner1-副标题',
      banner_1_cta: 'Banner1-按钮文字',
      banner_2_title: 'Banner2-标题',
      banner_2_subtitle: 'Banner2-副标题',
      banner_2_cta: 'Banner2-按钮文字',
      banner_3_title: 'Banner3-标题',
      banner_3_subtitle: 'Banner3-副标题',
      banner_3_cta: 'Banner3-按钮文字',
      banner_4_title: 'Banner4-标题',
      banner_4_subtitle: 'Banner4-副标题',
      banner_4_cta: 'Banner4-按钮文字',
      banner_5_title: 'Banner5-标题',
      banner_5_subtitle: 'Banner5-副标题',
      banner_5_cta: 'Banner5-按钮文字',

      // 首页英雄区域
      hero_title: '首页主标题',
      hero_subtitle: '首页副标题',
      hero_explore: '首页-探索',
      hero_learn: '首页-学习',
      hero_grow: '首页-成长',
      hero_button_text: '首页按钮文字',

      // 项目展示
      program_showcase_title: '项目展示-主标题',
      program_showcase_link_text: '项目展示-查看所有链接',

      // 关于我们
      about_title: '关于我们-标题',
      about_subtitle: '关于我们-副标题',
      about_description: '关于我们-描述',
      about_mission_title: '使命-标题',
      about_mission_description: '使命-描述',
      about_vision_title: '愿景-标题',
      about_vision_description: '愿景-描述',

      // 为什么选择EdGoing
      why_choose_title: '选择我们-标题',
      why_choose_subtitle: '选择我们-副标题',
      why_choose_expertise_title: '专业知识-标题',
      why_choose_expertise_description: '专业知识-描述',
      why_choose_global_title: '全球视野-标题',
      why_choose_global_description: '全球视野-描述',
      why_choose_safety_title: '安全承诺-标题',
      why_choose_safety_description: '安全承诺-描述',

      // 学员支持与安全
      student_support_title: '学员支持-标题',
      student_support_subtitle: '学员支持-副标题',
      support_leadership_title: '领导团队-标题',
      support_leadership_description: '领导团队-描述',
      support_education_title: '教育项目-标题',
      support_education_description: '教育项目-描述',
      support_accommodation_title: '住宿餐饮-标题',
      support_accommodation_description: '住宿餐饮-描述',
      support_247_title: '全天候支持-标题',
      support_247_description: '全天候支持-描述',
      support_cultural_title: '文化体验-标题',
      support_cultural_description: '文化体验-描述',
      support_academic_title: '学术卓越-标题',
      support_academic_description: '学术卓越-描述',

      // 学员故事
      testimonials_title: '学员故事-标题',
      testimonials_subtitle: '学员故事-副标题',
      testimonial_1_content: '学员故事1-内容',
      testimonial_1_author: '学员故事1-作者',
      testimonial_1_role: '学员故事1-身份',
      testimonial_1_program: '学员故事1-项目',
      testimonial_2_content: '学员故事2-内容',
      testimonial_2_author: '学员故事2-作者',
      testimonial_2_role: '学员故事2-身份',
      testimonial_2_program: '学员故事2-项目',
      testimonial_3_content: '学员故事3-内容',
      testimonial_3_author: '学员故事3-作者',
      testimonial_3_role: '学员故事3-身份',
      testimonial_3_program: '学员故事3-项目',

      // 邮件订阅
      newsletter_title: '邮件订阅-标题',
      newsletter_subtitle: '邮件订阅-副标题',
      newsletter_placeholder: '邮件订阅-占位符',
      newsletter_button: '邮件订阅-按钮',

      // 行动号召
      cta_title: '行动号召-标题',
      cta_subtitle: '行动号召-副标题',
      cta_button_text: '行动号召-按钮',

      // 页脚
      footer_navigation: '页脚-导航标题',
      footer_contact_us: '页脚-联系我们标题',
      footer_follow_us: '页脚-关注我们标题',
      footer_call_us: '页脚-电话标签',
      footer_email: '页脚-邮箱标签',
      footer_address: '页脚-地址标签',
      footer_locations: '页脚-地点',
      footer_shanghai: '页脚-上海',
      footer_shanghai_address: '页脚-上海地址',
      footer_shanghai_district: '页脚-上海区域',
      footer_singapore: '页脚-新加坡',
      footer_singapore_address: '页脚-新加坡地址',
      footer_singapore_postal: '页脚-新加坡邮编',
      footer_follow_description: '页脚-关注描述',
      footer_copyright: '页脚-版权',

      // 页脚导航链接
      footer_nav_home: '页脚导航-首页',
      footer_nav_world_study: '页脚导航-游学国际',
      footer_nav_china_study: '页脚导航-游学中国',
      footer_nav_blog: '页脚导航-博客',
      footer_nav_about: '页脚导航-关于我们',
      footer_nav_faq: '页脚导航-FAQ',
      footer_nav_contact: '页脚导航-联系我们',

      // 联系页面
      contact_title: '联系页面-标题',
      contact_subtitle: '联系页面-副标题',
      contact_form_name: '联系表单-姓名',
      contact_form_email: '联系表单-邮箱',
      contact_form_message: '联系表单-留言',
      contact_form_submit: '联系表单-提交',

      // 项目页面
      programs_title: '项目页面-标题',
      programs_subtitle: '项目页面-副标题',
      programs_search_placeholder: '项目搜索-占位符',
      programs_filter_country: '项目筛选-国家',
      programs_filter_type: '项目筛选-类型',
      programs_no_results: '项目页面-无结果',
    }
    return labels[key] || key
  }

  const isTextarea = (key: string) => {
    return key.includes('description') || key.includes('subtitle') || key.includes('content')
  }

  const getFieldGroups = () => {
    const groups = [
      {
        title: '网站基本信息',
        keys: ['site_title', 'site_description', 'site_keywords']
      },
      {
        title: '导航栏',
        keys: ['nav_home', 'nav_programs', 'nav_study_china', 'nav_about', 'nav_blog', 'nav_contact']
      },
      {
        title: 'Banner轮播图',
        keys: [
          'banner_1_title', 'banner_1_subtitle', 'banner_1_cta',
          'banner_2_title', 'banner_2_subtitle', 'banner_2_cta',
          'banner_3_title', 'banner_3_subtitle', 'banner_3_cta',
          'banner_4_title', 'banner_4_subtitle', 'banner_4_cta',
          'banner_5_title', 'banner_5_subtitle', 'banner_5_cta'
        ]
      },
      {
        title: '首页英雄区域',
        keys: ['hero_title', 'hero_subtitle', 'hero_explore', 'hero_learn', 'hero_grow', 'hero_button_text']
      },
      {
        title: '项目展示',
        keys: ['program_showcase_title', 'program_showcase_link_text']
      },
      {
        title: '为什么选择EdGoing',
        keys: [
          'why_choose_title', 'why_choose_subtitle',
          'why_choose_expertise_title', 'why_choose_expertise_description',
          'why_choose_global_title', 'why_choose_global_description',
          'why_choose_safety_title', 'why_choose_safety_description'
        ]
      },
      {
        title: '学员支持与安全',
        keys: [
          'student_support_title', 'student_support_subtitle',
          'support_leadership_title', 'support_leadership_description',
          'support_education_title', 'support_education_description',
          'support_accommodation_title', 'support_accommodation_description',
          'support_247_title', 'support_247_description',
          'support_cultural_title', 'support_cultural_description',
          'support_academic_title', 'support_academic_description'
        ]
      },
      {
        title: '学员故事',
        keys: [
          'testimonials_title', 'testimonials_subtitle',
          'testimonial_1_content', 'testimonial_1_author', 'testimonial_1_role', 'testimonial_1_program',
          'testimonial_2_content', 'testimonial_2_author', 'testimonial_2_role', 'testimonial_2_program',
          'testimonial_3_content', 'testimonial_3_author', 'testimonial_3_role', 'testimonial_3_program'
        ]
      },
      {
        title: '邮件订阅',
        keys: ['newsletter_title', 'newsletter_subtitle', 'newsletter_placeholder', 'newsletter_button']
      },
      {
        title: '行动号召',
        keys: ['cta_title', 'cta_subtitle', 'cta_button_text']
      },
      {
        title: '页脚信息',
        keys: [
          'footer_navigation', 'footer_contact_us', 'footer_follow_us',
          'footer_call_us', 'footer_email', 'footer_address', 'footer_locations',
          'footer_shanghai', 'footer_shanghai_address', 'footer_shanghai_district',
          'footer_singapore', 'footer_singapore_address', 'footer_singapore_postal',
          'footer_follow_description', 'footer_copyright'
        ]
      },
      {
        title: '页脚导航链接',
        keys: [
          'footer_nav_home', 'footer_nav_world_study', 'footer_nav_china_study',
          'footer_nav_blog', 'footer_nav_about', 'footer_nav_faq', 'footer_nav_contact'
        ]
      },
      {
        title: '联系页面',
        keys: ['contact_title', 'contact_subtitle', 'contact_form_name', 'contact_form_email', 'contact_form_message', 'contact_form_submit']
      },
      {
        title: '项目页面',
        keys: ['programs_title', 'programs_subtitle', 'programs_search_placeholder', 'programs_filter_country', 'programs_filter_type', 'programs_no_results']
      }
    ]

    // 只返回有数据的分组
    return groups.filter(group =>
      group.keys.some(key => settings.some(setting => setting.key === key))
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">内容管理</h1>
              <p className="text-gray-600">管理网站内容和文本</p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => router.push('/admin')}>
                返回仪表板
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Card>
            <CardHeader>
              <CardTitle>网站内容</CardTitle>
              <CardDescription>
                编辑网站上显示的文本内容
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                  {error}
                </div>
              )}

              {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                  {success}
                </div>
              )}

              <div className="space-y-8">
                {getFieldGroups().map((group) => (
                  <div key={group.title} className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                      {group.title}
                    </h3>
                    <div className="space-y-4">
                      {group.keys.map((key) => {
                        const setting = settings.find(s => s.key === key)
                        if (!setting) return null

                        return (
                          <div key={key}>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              {getFieldLabel(key)}
                            </label>
                            {isTextarea(key) ? (
                              <Textarea
                                value={formData[key] || ''}
                                onChange={(e) => handleInputChange(key, e.target.value)}
                                rows={key.includes('content') ? 4 : 3}
                                className="w-full"
                              />
                            ) : (
                              <Input
                                value={formData[key] || ''}
                                onChange={(e) => handleInputChange(key, e.target.value)}
                                className="w-full"
                              />
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <Button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2"
                  >
                    {saving ? '保存中...' : '保存更改'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
