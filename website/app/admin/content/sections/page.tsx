"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface Setting {
  id: string
  key: string
  value: string
  type: string
  language: string
}

export default function ContentSectionsPage() {
  const [settings, setSettings] = useState<Setting[]>([])
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [language, setLanguage] = useState('zh')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()

  useEffect(() => {
    fetchContent()
  }, [language])

  const fetchContent = async () => {
    try {
      const response = await fetch(`/api/admin/content?language=${language}`)
      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
        
        // Convert settings to form data
        const formValues: Record<string, string> = {}
        data.settings.forEach((setting: Setting) => {
          formValues[setting.key] = setting.value
        })
        setFormData(formValues)
      } else if (response.status === 401) {
        router.push('/admin/login')
      } else {
        setError('Failed to fetch content')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/content', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: formData,
          language,
        }),
      })

      if (response.ok) {
        setSuccess('内容更新成功！')
        setTimeout(() => setSuccess(''), 3000)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to update content')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const contentSections = {
    basic: {
      title: '网站基本信息',
      fields: ['site_title', 'site_description', 'site_keywords']
    },
    navigation: {
      title: '导航栏',
      fields: ['nav_home', 'nav_about', 'nav_programs', 'nav_contact']
    },
    hero: {
      title: '首页英雄区域',
      fields: ['hero_title', 'hero_subtitle', 'hero_explore', 'hero_learn', 'hero_grow', 'hero_button_text']
    },
    whyChoose: {
      title: '为什么选择EdGoing',
      fields: [
        'why_choose_title', 'why_choose_subtitle',
        'why_choose_expertise_title', 'why_choose_expertise_description',
        'why_choose_global_title', 'why_choose_global_description',
        'why_choose_safety_title', 'why_choose_safety_description'
      ]
    },
    support: {
      title: '学员支持与安全',
      fields: [
        'student_support_title', 'student_support_subtitle',
        'support_leadership_title', 'support_leadership_description',
        'support_education_title', 'support_education_description',
        'support_accommodation_title', 'support_accommodation_description',
        'support_247_title', 'support_247_description',
        'support_cultural_title', 'support_cultural_description',
        'support_academic_title', 'support_academic_description'
      ]
    },
    testimonials: {
      title: '学员故事',
      fields: [
        'testimonials_title', 'testimonials_subtitle',
        'testimonial_1_content', 'testimonial_1_author', 'testimonial_1_role', 'testimonial_1_program',
        'testimonial_2_content', 'testimonial_2_author', 'testimonial_2_role', 'testimonial_2_program',
        'testimonial_3_content', 'testimonial_3_author', 'testimonial_3_role', 'testimonial_3_program'
      ]
    },
    newsletter: {
      title: '邮件订阅',
      fields: ['newsletter_title', 'newsletter_subtitle', 'newsletter_placeholder', 'newsletter_button']
    },
    cta: {
      title: '行动号召',
      fields: ['cta_title', 'cta_subtitle', 'cta_button_text']
    },
    footer: {
      title: '页脚',
      fields: [
        'footer_description', 'footer_copyright',
        'footer_contact_title', 'footer_contact_address', 'footer_contact_phone', 'footer_contact_email',
        'footer_links_title', 'footer_social_title'
      ]
    }
  }

  const getFieldLabel = (key: string) => {
    const labels: Record<string, string> = {
      // 网站基本信息
      site_title: '网站标题',
      site_description: '网站描述',
      site_keywords: '网站关键词',
      
      // 导航栏
      nav_home: '首页',
      nav_about: '关于我们',
      nav_programs: '项目',
      nav_contact: '联系我们',
      
      // 首页英雄区域
      hero_title: '主标题',
      hero_subtitle: '副标题',
      hero_explore: '探索',
      hero_learn: '学习',
      hero_grow: '成长',
      hero_button_text: '按钮文字',
      
      // 为什么选择EdGoing
      why_choose_title: '标题',
      why_choose_subtitle: '副标题',
      why_choose_expertise_title: '专业知识-标题',
      why_choose_expertise_description: '专业知识-描述',
      why_choose_global_title: '全球视野-标题',
      why_choose_global_description: '全球视野-描述',
      why_choose_safety_title: '安全承诺-标题',
      why_choose_safety_description: '安全承诺-描述',
      
      // 学员支持与安全
      student_support_title: '标题',
      student_support_subtitle: '副标题',
      support_leadership_title: '领导团队-标题',
      support_leadership_description: '领导团队-描述',
      support_education_title: '教育项目-标题',
      support_education_description: '教育项目-描述',
      support_accommodation_title: '住宿餐饮-标题',
      support_accommodation_description: '住宿餐饮-描述',
      support_247_title: '全天候支持-标题',
      support_247_description: '全天候支持-描述',
      support_cultural_title: '文化体验-标题',
      support_cultural_description: '文化体验-描述',
      support_academic_title: '学术卓越-标题',
      support_academic_description: '学术卓越-描述',
      
      // 学员故事
      testimonials_title: '标题',
      testimonials_subtitle: '副标题',
      testimonial_1_content: '学员故事1-内容',
      testimonial_1_author: '学员故事1-作者',
      testimonial_1_role: '学员故事1-身份',
      testimonial_1_program: '学员故事1-项目',
      testimonial_2_content: '学员故事2-内容',
      testimonial_2_author: '学员故事2-作者',
      testimonial_2_role: '学员故事2-身份',
      testimonial_2_program: '学员故事2-项目',
      testimonial_3_content: '学员故事3-内容',
      testimonial_3_author: '学员故事3-作者',
      testimonial_3_role: '学员故事3-身份',
      testimonial_3_program: '学员故事3-项目',
      
      // 邮件订阅
      newsletter_title: '标题',
      newsletter_subtitle: '副标题',
      newsletter_placeholder: '占位符',
      newsletter_button: '按钮',
      
      // 行动号召
      cta_title: '标题',
      cta_subtitle: '副标题',
      cta_button_text: '按钮',
      
      // 页脚
      footer_description: '描述',
      footer_copyright: '版权',
      footer_contact_title: '联系标题',
      footer_contact_address: '地址',
      footer_contact_phone: '电话',
      footer_contact_email: '邮箱',
      footer_links_title: '链接标题',
      footer_social_title: '社交标题',
    }
    return labels[key] || key
  }

  const isTextarea = (key: string) => {
    return key.includes('description') || key.includes('subtitle') || key.includes('content')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">页面板块管理</h1>
              <p className="text-gray-600">按板块管理网站内容</p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => router.push('/admin')}>
                返回仪表板
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}

          <Tabs defaultValue="basic" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-9">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="navigation">导航</TabsTrigger>
              <TabsTrigger value="hero">首页</TabsTrigger>
              <TabsTrigger value="whyChoose">选择我们</TabsTrigger>
              <TabsTrigger value="support">学员支持</TabsTrigger>
              <TabsTrigger value="testimonials">学员故事</TabsTrigger>
              <TabsTrigger value="newsletter">邮件订阅</TabsTrigger>
              <TabsTrigger value="cta">行动号召</TabsTrigger>
              <TabsTrigger value="footer">页脚</TabsTrigger>
            </TabsList>

            {Object.entries(contentSections).map(([sectionKey, section]) => (
              <TabsContent key={sectionKey} value={sectionKey}>
                <Card>
                  <CardHeader>
                    <CardTitle>{section.title}</CardTitle>
                    <CardDescription>
                      编辑 {section.title} 相关内容
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {section.fields.map((fieldKey) => (
                        <div key={fieldKey}>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {getFieldLabel(fieldKey)}
                          </label>
                          {isTextarea(fieldKey) ? (
                            <Textarea
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                              rows={fieldKey.includes('content') ? 4 : 3}
                            />
                          ) : (
                            <Input
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}

            <div className="flex justify-end">
              <Button
                onClick={handleSave}
                disabled={saving}
                size="lg"
              >
                {saving ? '保存中...' : '保存所有更改'}
              </Button>
            </div>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
