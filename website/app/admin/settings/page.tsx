"use client"

import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function SettingsPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
              <p className="text-gray-600">Manage website settings and configuration</p>
            </div>
            <Button onClick={() => router.push('/admin')}>
              Back to Dashboard
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Content Management */}
            <Card>
              <CardHeader>
                <CardTitle>Content Management</CardTitle>
                <CardDescription>
                  Manage website text content
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/content')}
                  >
                    Edit Website Content
                  </Button>
                  <p className="text-sm text-gray-600">
                    Edit all text content that appears on the website
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Language Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Language Settings</CardTitle>
                <CardDescription>
                  Multilingual configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/content')}
                  >
                    Manage Translations
                  </Button>
                  <p className="text-sm text-gray-600">
                    Manage Chinese and English content
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* System Settings */}
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
                <CardDescription>
                  System configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/system')}
                    disabled
                  >
                    System Config
                  </Button>
                  <p className="text-sm text-gray-600">
                    Advanced system settings (Coming soon)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* SEO Settings */}
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
                <CardDescription>
                  Search engine optimization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/seo')}
                    disabled
                  >
                    SEO Configuration
                  </Button>
                  <p className="text-sm text-gray-600">
                    Meta tags, sitemap, robots.txt (Coming soon)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Email Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Email Settings</CardTitle>
                <CardDescription>
                  Email configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/email')}
                    disabled
                  >
                    Email Config
                  </Button>
                  <p className="text-sm text-gray-600">
                    SMTP settings, templates (Coming soon)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Backup & Export */}
            <Card>
              <CardHeader>
                <CardTitle>Backup & Export</CardTitle>
                <CardDescription>
                  Data management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/admin/backup')}
                    disabled
                  >
                    Backup Data
                  </Button>
                  <p className="text-sm text-gray-600">
                    Export and backup system data (Coming soon)
                  </p>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </main>
    </div>
  )
}
