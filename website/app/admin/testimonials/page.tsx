"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Quote, Eye } from 'lucide-react'
import ImageUpload from '@/components/ImageUpload'
import Image from 'next/image'

interface Testimonial {
  id: string
  content: string
  author: string
  role: string
  program: string
  status: string
  language: string
  order: number
  image?: {
    id: string
    url: string
    alt?: string
  }
  authorUser: {
    id: string
    name: string
    username: string
  }
  createdAt: string
  updatedAt: string
}

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([])
  const [language, setLanguage] = useState('zh')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingTestimonial, setEditingTestimonial] = useState<Testimonial | null>(null)
  const [formData, setFormData] = useState({
    content: '',
    author: '',
    role: '',
    program: '',
    status: 'PUBLISHED',
    order: 0,
    imageId: '',
  })
  const [selectedImage, setSelectedImage] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    fetchTestimonials()
  }, [language])

  const fetchTestimonials = async () => {
    try {
      const response = await fetch(`/api/admin/testimonials?language=${language}`)
      if (response.ok) {
        const data = await response.json()
        setTestimonials(data.testimonials)
      } else if (response.status === 401) {
        router.push('/admin/login')
      } else {
        setError('Failed to fetch testimonials')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const resetForm = () => {
    setFormData({
      content: '',
      author: '',
      role: '',
      program: '',
      status: 'PUBLISHED',
      order: 0,
      imageId: '',
    })
    setSelectedImage(null)
    setEditingTestimonial(null)
    setShowCreateForm(false)
  }

  const handleCreate = () => {
    resetForm()
    setShowCreateForm(true)
  }

  const handleEdit = (testimonial: Testimonial) => {
    setFormData({
      content: testimonial.content,
      author: testimonial.author,
      role: testimonial.role,
      program: testimonial.program,
      status: testimonial.status,
      order: testimonial.order,
      imageId: testimonial.image?.id || '',
    })
    setSelectedImage(testimonial.image || null)
    setEditingTestimonial(testimonial)
    setShowCreateForm(true)
  }

  const handleSave = async () => {
    setSaving(true)
    setError('')
    setSuccess('')

    try {
      const payload = {
        ...formData,
        imageId: selectedImage?.id || null,
        language,
      }

      const url = editingTestimonial
        ? `/api/admin/testimonials/${editingTestimonial.id}`
        : '/api/admin/testimonials'

      const method = editingTestimonial ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        setSuccess(editingTestimonial ? '学员故事更新成功！' : '学员故事创建成功！')
        setTimeout(() => setSuccess(''), 3000)
        resetForm()
        fetchTestimonials()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to save testimonial')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个学员故事吗？')) return

    try {
      const response = await fetch(`/api/admin/testimonials/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setSuccess('学员故事删除成功！')
        setTimeout(() => setSuccess(''), 3000)
        fetchTestimonials()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to delete testimonial')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    }
  }

  const handleImageUploaded = (media: any) => {
    setSelectedImage(media)
  }

  const handleImageRemoved = () => {
    setSelectedImage(null)
  }



  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Quote className="w-8 h-8 text-blue-600" />
                学员故事管理
              </h1>
              <p className="text-gray-600">管理网站上展示的学员故事和感言</p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleCreate} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                添加学员故事
              </Button>
              <Button onClick={() => router.push('/admin')}>
                返回仪表板
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}

          <Tabs defaultValue="general" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">总体设置</TabsTrigger>
              <TabsTrigger value="testimonial1">学员故事 #1</TabsTrigger>
              <TabsTrigger value="testimonial2">学员故事 #2</TabsTrigger>
              <TabsTrigger value="testimonial3">学员故事 #3</TabsTrigger>
            </TabsList>

            {Object.entries(testimonialSections).map(([sectionKey, section]) => (
              <TabsContent key={sectionKey} value={sectionKey}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {section.icon}
                      {section.title}
                    </CardTitle>
                    <CardDescription>
                      {sectionKey === 'general' ? '设置学员故事板块的整体标题和描述' :
                       `编辑第${sectionKey.slice(-1)}个学员故事的详细信息`}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {section.fields.map((fieldKey) => (
                        <div key={fieldKey} className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            {getFieldLabel(fieldKey)}
                            {fieldKey.includes('content') && (
                              <Badge variant="secondary" className="ml-2">必填</Badge>
                            )}
                          </label>
                          {getFieldDescription(fieldKey) && (
                            <p className="text-xs text-gray-500">{getFieldDescription(fieldKey)}</p>
                          )}
                          {isTextarea(fieldKey) ? (
                            <Textarea
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                              rows={fieldKey.includes('content') ? 4 : 2}
                              placeholder={fieldKey.includes('content') ? '请输入学员的故事或感言...' : ''}
                            />
                          ) : (
                            <Input
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                              placeholder={
                                fieldKey.includes('author') ? '学员姓名' :
                                fieldKey.includes('role') ? '如：高中生、大学生等' :
                                fieldKey.includes('program') ? '项目名称' : ''
                              }
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}

            <div className="flex justify-end">
              <Button
                onClick={handleSave}
                disabled={saving}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {saving ? '保存中...' : '保存学员故事'}
              </Button>
            </div>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
