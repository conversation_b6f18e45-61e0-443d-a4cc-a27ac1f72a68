"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { User, Quote, GraduationCap } from 'lucide-react'

interface Setting {
  id: string
  key: string
  value: string
  type: string
  language: string
}

export default function TestimonialsPage() {
  const [settings, setSettings] = useState<Setting[]>([])
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [language, setLanguage] = useState('zh')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()

  useEffect(() => {
    fetchContent()
  }, [language])

  const fetchContent = async () => {
    try {
      const response = await fetch(`/api/admin/content?language=${language}`)
      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
        
        // Convert settings to form data
        const formValues: Record<string, string> = {}
        data.settings.forEach((setting: Setting) => {
          formValues[setting.key] = setting.value
        })
        setFormData(formValues)
      } else if (response.status === 401) {
        router.push('/admin/login')
      } else {
        setError('Failed to fetch content')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/content', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: formData,
          language,
        }),
      })

      if (response.ok) {
        setSuccess('学员故事更新成功！')
        setTimeout(() => setSuccess(''), 3000)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to update content')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const testimonialSections = {
    general: {
      title: '学员故事总体设置',
      icon: <Quote className="w-5 h-5" />,
      fields: ['testimonials_title', 'testimonials_subtitle']
    },
    testimonial1: {
      title: '学员故事 #1',
      icon: <User className="w-5 h-5" />,
      fields: ['testimonial_1_content', 'testimonial_1_author', 'testimonial_1_role', 'testimonial_1_program']
    },
    testimonial2: {
      title: '学员故事 #2', 
      icon: <User className="w-5 h-5" />,
      fields: ['testimonial_2_content', 'testimonial_2_author', 'testimonial_2_role', 'testimonial_2_program']
    },
    testimonial3: {
      title: '学员故事 #3',
      icon: <User className="w-5 h-5" />,
      fields: ['testimonial_3_content', 'testimonial_3_author', 'testimonial_3_role', 'testimonial_3_program']
    }
  }

  const getFieldLabel = (key: string) => {
    const labels: Record<string, string> = {
      // 总体设置
      testimonials_title: '板块标题',
      testimonials_subtitle: '板块副标题',
      
      // 学员故事1
      testimonial_1_content: '故事内容',
      testimonial_1_author: '学员姓名',
      testimonial_1_role: '学员身份',
      testimonial_1_program: '参与项目',
      
      // 学员故事2
      testimonial_2_content: '故事内容',
      testimonial_2_author: '学员姓名',
      testimonial_2_role: '学员身份',
      testimonial_2_program: '参与项目',
      
      // 学员故事3
      testimonial_3_content: '故事内容',
      testimonial_3_author: '学员姓名',
      testimonial_3_role: '学员身份',
      testimonial_3_program: '参与项目',
    }
    return labels[key] || key
  }

  const getFieldDescription = (key: string) => {
    const descriptions: Record<string, string> = {
      testimonials_title: '显示在学员故事板块顶部的主标题',
      testimonials_subtitle: '主标题下方的说明文字',
      testimonial_1_content: '学员的完整故事或感言，建议100-200字',
      testimonial_1_author: '学员的真实姓名或化名',
      testimonial_1_role: '如：高中生、大学生、学生家长等',
      testimonial_1_program: '学员参与的具体项目名称',
    }
    return descriptions[key] || ''
  }

  const isTextarea = (key: string) => {
    return key.includes('content') || key.includes('subtitle')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Quote className="w-8 h-8 text-blue-600" />
                学员故事管理
              </h1>
              <p className="text-gray-600">管理网站上展示的学员故事和感言</p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => router.push('/admin')}>
                返回仪表板
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}

          <Tabs defaultValue="general" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">总体设置</TabsTrigger>
              <TabsTrigger value="testimonial1">学员故事 #1</TabsTrigger>
              <TabsTrigger value="testimonial2">学员故事 #2</TabsTrigger>
              <TabsTrigger value="testimonial3">学员故事 #3</TabsTrigger>
            </TabsList>

            {Object.entries(testimonialSections).map(([sectionKey, section]) => (
              <TabsContent key={sectionKey} value={sectionKey}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {section.icon}
                      {section.title}
                    </CardTitle>
                    <CardDescription>
                      {sectionKey === 'general' ? '设置学员故事板块的整体标题和描述' : 
                       `编辑第${sectionKey.slice(-1)}个学员故事的详细信息`}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {section.fields.map((fieldKey) => (
                        <div key={fieldKey} className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            {getFieldLabel(fieldKey)}
                            {fieldKey.includes('content') && (
                              <Badge variant="secondary" className="ml-2">必填</Badge>
                            )}
                          </label>
                          {getFieldDescription(fieldKey) && (
                            <p className="text-xs text-gray-500">{getFieldDescription(fieldKey)}</p>
                          )}
                          {isTextarea(fieldKey) ? (
                            <Textarea
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                              rows={fieldKey.includes('content') ? 4 : 2}
                              placeholder={fieldKey.includes('content') ? '请输入学员的故事或感言...' : ''}
                            />
                          ) : (
                            <Input
                              value={formData[fieldKey] || ''}
                              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                              placeholder={
                                fieldKey.includes('author') ? '学员姓名' :
                                fieldKey.includes('role') ? '如：高中生、大学生等' :
                                fieldKey.includes('program') ? '项目名称' : ''
                              }
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}

            <div className="flex justify-end">
              <Button
                onClick={handleSave}
                disabled={saving}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {saving ? '保存中...' : '保存学员故事'}
              </Button>
            </div>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
