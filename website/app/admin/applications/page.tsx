"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

interface Application {
  id: string
  studentName: string
  studentEmail: string
  studentPhone?: string
  studentAge?: number
  parentName?: string
  parentEmail?: string
  parentPhone?: string
  status: string
  notes?: string
  submittedAt: string
  program: {
    id: string
    title: string
    slug: string
    country: string
    city: string
  }
}

export default function ApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [editingApp, setEditingApp] = useState<string | null>(null)
  const [editStatus, setEditStatus] = useState('')
  const [editNotes, setEditNotes] = useState('')
  const router = useRouter()

  useEffect(() => {
    fetchApplications()
  }, [])

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/admin/applications')
      if (response.ok) {
        const data = await response.json()
        setApplications(data.applications)
      } else if (response.status === 401) {
        router.push('/admin/login')
      } else {
        setError('Failed to fetch applications')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (app: Application) => {
    setEditingApp(app.id)
    setEditStatus(app.status)
    setEditNotes(app.notes || '')
  }

  const handleSave = async (id: string) => {
    try {
      const response = await fetch('/api/admin/applications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          status: editStatus,
          notes: editNotes,
        }),
      })

      if (response.ok) {
        await fetchApplications()
        setEditingApp(null)
      } else {
        setError('Failed to update application')
      }
    } catch (error) {
      setError('Network error')
    }
  }

  const handleCancel = () => {
    setEditingApp(null)
    setEditStatus('')
    setEditNotes('')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      case 'WAITLIST':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Applications</h1>
              <p className="text-gray-600">Manage program applications</p>
            </div>
            <Button onClick={() => router.push('/admin')}>
              Back to Dashboard
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="grid gap-6">
            {applications.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-gray-500">No applications found</p>
                </CardContent>
              </Card>
            ) : (
              applications.map((app) => (
                <Card key={app.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{app.studentName}</CardTitle>
                        <CardDescription>
                          {app.program.title} • {app.program.country}, {app.program.city}
                        </CardDescription>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(app.status)}`}>
                        {app.status}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="font-semibold text-gray-900">Student Information</h4>
                        <p className="text-sm text-gray-600">Email: {app.studentEmail}</p>
                        {app.studentPhone && <p className="text-sm text-gray-600">Phone: {app.studentPhone}</p>}
                        {app.studentAge && <p className="text-sm text-gray-600">Age: {app.studentAge}</p>}
                      </div>
                      {(app.parentName || app.parentEmail) && (
                        <div>
                          <h4 className="font-semibold text-gray-900">Parent Information</h4>
                          {app.parentName && <p className="text-sm text-gray-600">Name: {app.parentName}</p>}
                          {app.parentEmail && <p className="text-sm text-gray-600">Email: {app.parentEmail}</p>}
                          {app.parentPhone && <p className="text-sm text-gray-600">Phone: {app.parentPhone}</p>}
                        </div>
                      )}
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600">
                        Submitted: {new Date(app.submittedAt).toLocaleDateString()}
                      </p>
                    </div>

                    {editingApp === app.id ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Status
                          </label>
                          <Select value={editStatus} onValueChange={setEditStatus}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="PENDING">Pending</SelectItem>
                              <SelectItem value="APPROVED">Approved</SelectItem>
                              <SelectItem value="REJECTED">Rejected</SelectItem>
                              <SelectItem value="WAITLIST">Waitlist</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Notes
                          </label>
                          <Textarea
                            value={editNotes}
                            onChange={(e) => setEditNotes(e.target.value)}
                            rows={3}
                            placeholder="Add notes about this application..."
                          />
                        </div>
                        <div className="flex space-x-2">
                          <Button onClick={() => handleSave(app.id)}>
                            Save
                          </Button>
                          <Button variant="outline" onClick={handleCancel}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        {app.notes && (
                          <div className="mb-4">
                            <h4 className="font-semibold text-gray-900 mb-2">Notes</h4>
                            <p className="text-sm text-gray-600">{app.notes}</p>
                          </div>
                        )}
                        <Button onClick={() => handleEdit(app)}>
                          Edit Application
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
