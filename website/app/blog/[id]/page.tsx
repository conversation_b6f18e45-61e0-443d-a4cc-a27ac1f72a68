"use client"

import type React from "react"
import PageHero from "@/components/PageHero"
import Footer from "@/components/Footer"
import Image from "next/image"
import { CalendarDays, User } from "lucide-react"
import Slider from "react-slick"
import "slick-carousel/slick/slick.css"
import "slick-carousel/slick/slick-theme.css"

// 模拟博客数据
const blogPost = {
  id: 1,
  title: "10 Tips for Studying Abroad",
  author: "<PERSON>",
  date: "2023-06-15",
  content: `
    <p>Studying abroad can be one of the most exciting and rewarding experiences of your academic career. Here are 10 tips to help you make the most of your time abroad:</p>
    <ol>
      <li><strong>Research your destination:</strong> Learn about the culture, customs, and laws of the country you'll be visiting.</li>
      <li><strong>Learn the language basics:</strong> Even if you're studying in an English-speaking country, learning a few local phrases can go a long way.</li>
      <li><strong>Pack smart:</strong> Bring essentials, but don't overpack. You can usually buy what you need once you arrive.</li>
      <li><strong>Stay open-minded:</strong> Embrace new experiences and be willing to step out of your comfort zone.</li>
      <li><strong>Make local friends:</strong> Don't just stick with other international students. Try to connect with locals to truly immerse yourself in the culture.</li>
      <li><strong>Keep a journal or blog:</strong> Document your experiences. You'll appreciate having these memories later.</li>
      <li><strong>Manage your finances:</strong> Create a budget and stick to it. Look for student discounts and free activities.</li>
      <li><strong>Stay safe:</strong> Be aware of your surroundings and take necessary precautions, just as you would at home.</li>
      <li><strong>Travel when you can:</strong> Take advantage of your location to explore nearby cities or countries during breaks.</li>
      <li><strong>Reflect on your experience:</strong> Take time to think about how your time abroad is changing you and shaping your worldview.</li>
    </ol>
    <p>Remember, studying abroad is what you make of it. Stay positive, be proactive, and make the most of this incredible opportunity!</p>
  `,
  images: [
    "https://images.unsplash.com/photo-1517486808906-6ca8b3f04846?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1519181245277-cffeb31da2e3?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1524850011238-e3d235c7d4c9?q=80&w=2074&fit=crop",
    "https://images.unsplash.com/photo-1503457574462-bd27054394c1?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1498354178607-a79df2916198?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1527269534026-c86f4009eace?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1513366208864-87536b8bd7b4?q=80&w=2074&fit=crop",
    "https://images.unsplash.com/photo-1516321497487-e288fb19713f?q=80&w=2070&fit=crop",
    "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&fit=crop",
  ],
  category: "Study Tips",
  tags: ["Study Abroad", "Travel", "Education", "Cultural Exchange"],
}

const ImageCarousel: React.FC<{ images: string[] }> = ({ images }) => {
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  }

  return (
    <div className="mb-8">
      <Slider {...settings}>
        {images.map((image, index) => (
          <div key={index} className="relative h-[400px]">
            <Image
              src={image || "/placeholder.svg"}
              alt={`Blog image ${index + 1}`}
              fill
              className="object-cover rounded-lg"
            />
          </div>
        ))}
      </Slider>
    </div>
  )
}

const BlogContent: React.FC<{ post: typeof blogPost }> = ({ post }) => {
  return (
    <article className="max-w-3xl mx-auto px-4 py-8">
      <ImageCarousel images={post.images} />
      <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
      <div className="flex items-center text-gray-600 mb-6 space-x-4">
        <div className="flex items-center">
          <User className="w-5 h-5 mr-2" />
          <span>{post.author}</span>
        </div>
        <div className="flex items-center">
          <CalendarDays className="w-5 h-5 mr-2" />
          <span>{post.date}</span>
        </div>
      </div>
      <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: post.content }} />
    </article>
  )
}

export default function BlogPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <PageHero
        title={blogPost.title}
        description="Insights and tips for your study abroad journey"
        backgroundImage={blogPost.images[0]}
      />
      <main className="flex-grow bg-white">
        <BlogContent post={blogPost} />
      </main>
      <Footer />
    </div>
  )
}
