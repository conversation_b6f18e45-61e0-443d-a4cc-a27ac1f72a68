"use client"

import { useContent } from '@/hooks/useContent'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LanguageSwitcher } from '@/components/LanguageSwitcher'

export default function DemoPage() {
  const { getContent, loading } = useContent()
  const { t } = useTranslation()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              CMS Demo Page
            </h1>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Dynamic Content Demo</CardTitle>
                <CardDescription>
                  This page demonstrates how content can be managed dynamically through the CMS
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  The content below is loaded from the database and can be edited through the admin panel.
                  Try switching languages to see the multilingual support in action.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Site Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Site Title:</h3>
                  <p className="text-gray-600">
                    {getContent('site_title', 'EdGoing - Default Title')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Site Description:</h3>
                  <p className="text-gray-600">
                    {getContent('site_description', 'Default site description')}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hero Section</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Hero Title:</h3>
                  <p className="text-gray-600">
                    {getContent('hero_title', 'Default Hero Title')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Hero Subtitle:</h3>
                  <p className="text-gray-600">
                    {getContent('hero_subtitle', 'Default hero subtitle')}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Call to Action</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">CTA Title:</h3>
                  <p className="text-gray-600">
                    {getContent('cta_title', 'Default CTA Title')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">CTA Description:</h3>
                  <p className="text-gray-600">
                    {getContent('cta_description', 'Default CTA description')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">CTA Button Text:</h3>
                  <p className="text-gray-600">
                    {getContent('cta_button_text', 'Default Button Text')}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Newsletter Section</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Newsletter Title:</h3>
                  <p className="text-gray-600">
                    {getContent('newsletter_title', 'Default Newsletter Title')}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Newsletter Description:</h3>
                  <p className="text-gray-600">
                    {getContent('newsletter_description', 'Default newsletter description')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>How to Edit Content</CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Go to <a href="/admin/login" className="text-blue-600 hover:underline">/admin/login</a></li>
                  <li>Login with: <EMAIL> / admin123</li>
                  <li>Click on "General Settings" or go to <a href="/admin/content" className="text-blue-600 hover:underline">/admin/content</a></li>
                  <li>Select your language (中文/English)</li>
                  <li>Edit the content fields</li>
                  <li>Click "Save Changes"</li>
                  <li>Return to this page and refresh to see the changes</li>
                </ol>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
