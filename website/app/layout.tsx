import type React from "react"
import "./globals.css"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import Navbar from "@/components/Navbar"
import { I18nProvider } from "@/components/I18nProvider"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

export const metadata: Metadata = {
  title: "YOUNICKO - Explore. Learn. Grow.",
  description: "Explore study abroad programs and activities",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans">
        <I18nProvider>
          <Navbar />
          {children}
        </I18nProvider>
      </body>
    </html>
  )
}
