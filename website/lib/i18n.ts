import i18n from "i18next"
import { initReactI18next } from "react-i18next"

// Define the resources directly in the file to avoid import issues
const resources = {
  en: {
    common: {
      nav: {
        home: "Home",
        programs: "Study Abroad",
        studyChina: "Study in China",
        about: "About EdGoing",
        blog: "Blog",
        contact: "Start Project",
      },
      program: {
        projectType: "Project Type",
        academicFocus: "Academic Focus",
        city: "City",
        gradeLevel: "Grade Level",
        sessions: "Sessions",
        deadline: "Deadline",
        innovation: "Innovation Research",
      },
      countries: {
        singapore: "Singapore",
        usa: "United States",
        uk: "United Kingdom",
        canada: "Canada",
      },
      grades: {
        middleSchool: "Middle School",
        highSchool: "High School",
        university: "University",
      },
      breadcrumb: {
        learn: "Learn",
        observe: "Observe",
        create: "Create",
        think: "Think",
      },
      tabs: {
        highlights: "Highlights",
        academics: "Academics",
        itinerary: "Itinerary",
        admission: "Admission",
      },
      sections: {
        projectHighlights: "Project Highlights",
        academicContent: "Academic Content",
        itinerary: "Itinerary",
        admissionRequirements: "Admission Requirements",
      },
      academics: {
        intro:
          "This program combines theoretical learning with practical experience, covering the following academic modules:",
      },
      itinerary: {
        week1: "Week 1: Foundation Learning",
        week2: "Week 2: Advanced Practice",
      },
      admission: {
        basicRequirements: "Basic Requirements",
        applicationMaterials: "Application Materials",
      },
      image: "Image",
      hero: {
        explore: "Explore.",
        learn: "Learn.",
        grow: "Grow.",
        subtitle: "Your Lifetime Itinerary Awaits.",
      },
      banner: {
        summer2025: {
          title: "2025 Summer Study Tour",
          subtitle: "Deep-dive programs in 20+ global universities",
          cta: "Learn More",
        },
        ukElite: {
          title: "UK Elite University Tour",
          subtitle: "Explore Oxford, Cambridge and more",
          cta: "Discover Now",
        },
        japanTech: {
          title: "Japan Tech Innovation Tour",
          subtitle: "Where cutting-edge meets tradition",
          cta: "Join the Innovation",
        },
        leadership: {
          title: "Global Leadership Summit",
          subtitle: "Empowering the next generation of world leaders",
          cta: "Be a Leader",
        },
        sustainable: {
          title: "Sustainable Future Program",
          subtitle: "Learn to shape a greener tomorrow",
          cta: "Go Green",
        },
      },
      whyChoose: {
        title: "Student Support and Safety",
        subtitle:
          "We prioritize your health and safety first, providing comprehensive support services throughout your educational journey.",
        leadership: {
          title: "Experienced Leadership Team",
          description:
            "Our experienced team provides professional guidance and comprehensive support throughout your journey.",
        },
        education: {
          title: "High-Quality Education Programs",
          description:
            "Carefully designed programs with top partner institutions to provide excellent educational experiences.",
        },
        accommodation: {
          title: "Safe Accommodation and Healthy Dining",
          description: "Safe living environments and nutritious meals to ensure your health and well-being.",
        },
        support247: {
          title: "24/7 Support",
          description: "Round-the-clock support services to provide continuous assistance for students.",
        },
        cultural: {
          title: "Immersive Cultural Experience",
          description: "Interactive activities and rich travel experiences to promote deep cultural learning.",
        },
        academic: {
          title: "Recognized Academic Excellence",
          description: "Courses recognized by top universities, enhancing your academic credentials.",
        },
      },
      whyChooseEdGoing: {
        title: "Why Choose",
        subtitle: "Inspiring global thinking through transformative, safe and personalized journeys, trusted worldwide",
        features: {
          expertise: {
            title: "Professional Knowledge & Experience",
            description: "EdGoing is rooted in global expertise, committed to rigorous research, and carefully crafts the highest quality, transformative educational programs that inspire students to learn beyond the classroom.",
          },
          globalVision: {
            title: "Global Vision",
            description: "Through strategic global partnerships, EdGoing creates authentic cultural exchanges, enabling students to become well-informed, empathetic future leaders with a global perspective.",
          },
          safety: {
            title: "Safety & Personalization Commitment",
            description: "EdGoing designs safe, high-quality and personalized journeys to help students master lifelong skills and gain transformative global perspectives.",
          },
        },
      },
      testimonials: {
        title: "Student Stories",
        subtitle: "Listen to our students share their learning experiences and growth stories.",
        items: {
          student1: {
            content: "My study tour experience in Singapore was amazing. Not only did I learn a lot of scientific knowledge, but I also experienced different cultures. The teachers were very professional and the courses were designed to be very interesting, which made me more interested in science. This trip really changed my view of the world.",
            author: "Zhang Wenhui",
            role: "High School Student",
            program: "Singapore Science Camp",
          },
          parent1: {
            content: "Participating in EdGoing's program made my child more confident and independent. She not only improved her English level, but also learned how to get along with classmates from different cultural backgrounds. This was a very worthwhile investment.",
            author: "Li Ming",
            role: "Parent",
            program: "International Cultural Exchange Program",
          },
          student2: {
            content: "Through EdGoing's STEM program, I developed a strong interest in programming and robotics. The mentors were very professional and the teaching methods were very interesting, allowing me to learn a lot while playing.",
            author: "Wang Xiaoming",
            role: "Middle School Student",
            program: "STEM Innovation Camp",
          },
        },
      },
      newsletter: {
        title: "Subscribe to Our Latest News",
        subtitle: "Subscribe to our learning newsletter to get learning resources and content, and receive special offers.",
        placeholder: "Enter your email address",
        button: "Subscribe",
      },
      cta: {
        title: "Ready to Start Your Journey?",
        subtitle: "Take the first step in your international education adventure. Our team is ready to help you plan the perfect program.",
        button: "Start Consultation",
      },
      footer: {
        navigation: "NAVIGATION",
        contactUs: "CONTACT US",
        followUs: "FOLLOW US",
        nav: {
          home: "Home",
          worldStudy: "Study Abroad",
          chinaStudy: "Study in China",
          blog: "Blog",
          about: "About EdGoing",
          faq: "FAQ",
          contact: "Start Project",
        },
        callUs: "Call Us",
        email: "Email",
        address: "Address",
        locations: "Shanghai | Singapore",
        shanghai: "Shanghai",
        shanghaiAddress: "18F, Tower B, 838 South Huangpi Road",
        shanghaiDistrict: "Huangpu District, Shanghai, 200025",
        singapore: "Singapore",
        singaporeAddress: "9 Kelantan Lane #06-01",
        singaporePostal: "Singapore 208628",
        followDescription: "Follow us on social media for updates and educational insights",
        copyright: "2025 EdGoing. All rights reserved.",
      },
      partners: {
        title: "Our Partners",
        subtitle:
          "We have established deep partnerships with top universities and educational institutions worldwide to provide quality educational resources and learning opportunities for our students.",
        universities: "Partner Universities",
        organizations: "Partner Organizations",
        stats: {
          universities: "Partner Universities",
          countries: "Countries Covered",
          programs: "Programs Available",
          students: "Students Benefited",
        },
      },
    },
    programs: {
      singaporeAI: {
        title: "Singapore Innovation Camp & 7-Day Academic Journey",
        description:
          "This is a two-week immersive learning program that combines lectures, corporate visits, cultural workshops, and team projects to explore how Singapore has developed strong digital innovation capabilities from its traditional cultural foundation. The program is designed for global students, combining SMU's fintech and innovation research advantages to provide an immersive innovation practice experience in Singapore's local innovation ecosystem.",
        sessions: {
          session1: "July 1-14, 2025",
          session2: "July 15-28, 2025",
          session3: "August 1-14, 2025",
        },
        deadline: "June 15, 2025",
        highlights: {
          highlight1:
            "Deep experience of the perfect integration of Singapore's traditional culture and modern innovation",
          highlight2: "Visit renowned companies to understand Singapore's digital economy development model",
          highlight3: "Exchange with local students to enhance cross-cultural communication skills",
          highlight4: "Professional mentor guidance and academic certification",
          highlight5: "Immersive language environment to rapidly improve English proficiency",
        },
        academics: {
          academic1: "Artificial Intelligence Fundamentals and Applications",
          academic2: "Data Science and Machine Learning",
          academic3: "Innovation Thinking and Design Thinking",
          academic4: "Cross-cultural Business Communication",
          academic5: "Project Management and Team Collaboration",
        },
        itinerary: {
          item1: "Professional course learning",
          item2: "Campus visits and experiences",
          item3: "Cultural exchange activities",
          item4: "Team building activities",
          item5: "Practical project participation",
          item6: "Expert lectures",
          item7: "Field investigations",
          item8: "Results presentation",
        },
        admission: {
          req1: "Age: 16-25 years old",
          req2: "Education: High school or above",
          req3: "Language: Basic English communication skills",
          req4: "Healthy and adaptable to group living",
          material1: "Application form",
          material2: "Personal statement",
          material3: "Academic transcripts",
          material4: "Recommendation letter (optional)",
          material5: "Passport copy",
        },
      },
      usArt: {
        title: "Youth Art Advanced Program",
        description:
          "You Nic Co collaborates with Mass Audubon to present the Youth Art Advanced Program, perfectly combining art and nature to inspire students' creativity and imagination. Through visits to nature reserves and learning about flora and fauna, students will create art under the guidance of experienced art teachers.",
        sessions: {
          session1: "July 1-14, 2025",
          session2: "July 15-28, 2025",
          session3: "August 1-14, 2025",
        },
        deadline: "June 15, 2025",
        highlights: {
          highlight1: "Instruction by experienced art teachers, cultivating proper painting habits in students",
          highlight2: "Study alongside students from around the world, improving English speaking skills",
          highlight3: "Carefully designed courses to expand students' creativity and imagination",
          highlight4:
            "Visit top art schools across the US, experiencing the artistic atmosphere of prestigious institutions",
          highlight5: "24/7 safety protection, ensuring students' personal safety throughout the course",
        },
        academics: {
          academic1: "Art fundamentals theory and techniques",
          academic2: "Nature observation and sketching skills",
          academic3: "Creative thinking development",
          academic4: "Art history and cultural background",
          academic5: "Portfolio creation guidance",
        },
        itinerary: {
          item1: "Art foundation course learning",
          item2: "Nature reserve visits and sketching",
          item3: "Art museum visits",
          item4: "Exchange with local artists",
          item5: "Creative practice projects",
          item6: "Art school visits",
          item7: "Exhibition preparation",
          item8: "Graduation exhibition",
        },
        admission: {
          req1: "Age: 14-18 years old",
          req2: "Education: Middle school or above",
          req3: "Language: Basic English communication skills",
          req4: "Strong interest in art",
          material1: "Application form",
          material2: "Personal portfolio (optional)",
          material3: "Academic transcripts",
          material4: "Recommendation letter (optional)",
          material5: "Passport copy",
        },
      },
      types: {
        innovation: "Innovation Camp",
        stem: "STEM & Science Innovation",
        academic: "Academic Study",
        cultural: "Cultural Experience",
      },
    },
  },
  zh: {
    common: {
      nav: {
        home: "首页",
        programs: "游学国际",
        studyChina: "游学中国",
        about: "关于EdGoing",
        blog: "博客",
        contact: "开始项目",
      },
      program: {
        projectType: "项目类型",
        academicFocus: "学术重点",
        city: "城市",
        gradeLevel: "年级",
        sessions: "营期",
        deadline: "截止日期",
        innovation: "创新研究",
      },
      countries: {
        singapore: "新加坡",
        usa: "美国",
        uk: "英国",
        canada: "加拿大",
      },
      grades: {
        middleSchool: "中学",
        highSchool: "高中",
        university: "大学",
      },
      breadcrumb: {
        learn: "学",
        observe: "观",
        create: "创",
        think: "思",
      },
      tabs: {
        highlights: "亮点",
        academics: "中学术",
        itinerary: "行程",
        admission: "入学",
      },
      sections: {
        projectHighlights: "项目亮点",
        academicContent: "学术内容",
        itinerary: "行程安排",
        admissionRequirements: "申请要求",
      },
      academics: {
        intro: "本项目结合理论学习与实践体验，涵盖以下学术模块：",
      },
      itinerary: {
        week1: "第一周：基础学习",
        week2: "第二周：深度实践",
      },
      admission: {
        basicRequirements: "基本要求",
        applicationMaterials: "申请材料",
      },
      image: "图片",
      hero: {
        explore: "探索。",
        learn: "学习。",
        grow: "成长。",
        subtitle: "您的终身学习之旅等待着您。",
      },
      banner: {
        summer2025: {
          title: "2025夏季游学计划启动",
          subtitle: "覆盖全球20+名校的深度体验项目",
          cta: "了解更多",
        },
        ukElite: {
          title: "英国顶尖学府深度游学",
          subtitle: "探索牛津剑桥等世界名校",
          cta: "立即探索",
        },
        japanTech: {
          title: "日本科技创新之旅",
          subtitle: "体验前沿科技与传统文化的碰撞",
          cta: "加入创新之旅",
        },
        leadership: {
          title: "全球青年领袖峰会",
          subtitle: "培养下一代全球领袖",
          cta: "成为领袖",
        },
        sustainable: {
          title: "可持续发展未来计划",
          subtitle: "学习塑造更绿色的明天",
          cta: "践行环保",
        },
      },
      whyChoose: {
        title: "学员支持与安全",
        subtitle: "我们优先考虑您的健康，在您的教育旅程中全天候提供全面的支持服务。",
        leadership: {
          title: "经验丰富的领导团队",
          description: "我们的专业团队具备丰富经验，为您的学习之旅提供专业指导和全面支持。",
        },
        education: {
          title: "高质量教育项目",
          description: "由专家主导的课程，与顶级合作伙伴合作，提供卓越的教育体验。",
        },
        accommodation: {
          title: "安全住宿和健康餐饮",
          description: "安全的住宿环境，以及适合各种饮食需求的营养餐饮，以确保您的健康。",
        },
        support247: {
          title: "全天候支持",
          description: "全天候服务，为学员提供持续支持。",
        },
        cultural: {
          title: "沉浸式文化体验",
          description: "互动活动，融入丰富的游览和当地参与，促进深度学习。",
        },
        academic: {
          title: "受认可的学术卓越",
          description: "首屈一指的高等学府表现和来自大学申请的课程。",
        },
      },
      whyChooseEdGoing: {
        title: "为什么选择",
        subtitle: "以变革性、安全和个性化的旅程激发全球思维，获得全球信赖",
        features: {
          expertise: {
            title: "专业知识与经验",
            description: "EdGoing 植根于全球专业知识，致力于严谨的研究，精心打造最高品质、变革性的教育项目，激励学员在课堂之外学习。",
          },
          globalVision: {
            title: "全球视野",
            description: "通过战略性的全球合作伙伴关系，EdGoing 创造了真实的文化交流，使学员成为几乎所有识广、富有同理心且具有全球视野的未来领导者。",
          },
          safety: {
            title: "安全与个性化承诺",
            description: "EdGoing 设计安全、高品质且个性化的旅程，帮助学员掌握终身技能并获得变革性的全球视野。",
          },
        },
      },
      testimonials: {
        title: "学员故事",
        subtitle: "听听我们学员分享他们的学习经历和成长故事。",
        items: {
          student1: {
            content: "我在新加坡的游学经历非常棒，不仅让我学到了很多科学知识，还让我体验了不同的文化。老师们都很专业，课程设计得很有趣，让我对科学产生了更大的兴趣。这次旅行真的改变了我对世界的看法。",
            author: "张文慧",
            role: "高中生",
            program: "新加坡科学营",
          },
          parent1: {
            content: "参加EdGoing的项目让我的孩子变得更加自信和独立。她不仅提高了英语水平，还学会了如何与来自不同文化背景的同学相处。这是一次非常值得的投资。",
            author: "李明",
            role: "学生家长",
            program: "国际文化交流项目",
          },
          student2: {
            content: "通过EdGoing的STEM项目，我对编程和机器人技术产生了浓厚的兴趣。导师们都很专业，教学方式很有趣，让我在玩中学到了很多知识。",
            author: "王小明",
            role: "初中生",
            program: "STEM创新营",
          },
        },
      },
      newsletter: {
        title: "订阅我们的最新消息",
        subtitle: "订阅我们的学习通讯，获取学习资源和内容，并获得优惠信息。",
        placeholder: "输入您的邮箱地址",
        button: "订阅",
      },
      cta: {
        title: "准备开始您的旅程？",
        subtitle: "迈出国际教育冒险的第一步，我们的团队随时为您提供规划完美项目的帮助。",
        button: "开始咨询",
      },
      footer: {
        navigation: "导航",
        contactUs: "联系我们",
        followUs: "关注我们",
        nav: {
          home: "首页",
          worldStudy: "游学国际",
          chinaStudy: "游学中国",
          blog: "博客",
          about: "关于EdGoing",
          faq: "常见问题",
          contact: "开始项目",
        },
        callUs: "联系电话",
        email: "邮箱",
        address: "地址",
        locations: "上海 | 新加坡",
        shanghai: "上海",
        shanghaiAddress: "上海市黄埔区黄陂南路838号",
        shanghaiDistrict: "中海国际B座18楼",
        singapore: "新加坡",
        singaporeAddress: "9 Kelantan Lane #06-01",
        singaporePostal: "Singapore 208628",
        followDescription: "通过社交媒体关注我们，了解最新动态和教育资讯",
        copyright: "2025 引里信息咨询（上海）有限公司 版权所有",
      },
      partners: {
        title: "合作伙伴",
        subtitle: "与全球顶尖院校和教育机构建立深度合作关系，为学员提供优质的教育资源和学习机会。",
        universities: "合作院校",
        organizations: "合作机构",
        stats: {
          universities: "合作院校",
          countries: "覆盖国家",
          programs: "项目数量",
          students: "受益学员",
        },
      },
    },
    programs: {
      singaporeAI: {
        title: "新加坡创意营理课程暨7天学术之旅",
        description:
          "本项目是一个为期两周的深度体验式学习项目，融合讲座课堂、企业参访、文化工作坊与团队项目，以探索新加坡如何从传统文化根基发展出强劲的数字创新力量。项目面向全球学生，结合SMU的金融科技与创新研究优势，深入新加坡本地创新生态，带来沉浸式的创新实践体验。",
        sessions: {
          session1: "2025年7月1日 - 7月14日",
          session2: "2025年7月15日 - 7月28日",
          session3: "2025年8月1日 - 8月14日",
        },
        deadline: "2025年6月15日",
        highlights: {
          highlight1: "深度体验新加坡传统文化与现代创新的完美融合",
          highlight2: "参访知名企业，了解新加坡数字经济发展模式",
          highlight3: "与当地学生交流，提升跨文化沟通能力",
          highlight4: "专业导师指导，获得学术认证",
          highlight5: "沉浸式语言环境，快速提升英语水平",
        },
        academics: {
          academic1: "人工智能基础理论与应用",
          academic2: "数据科学与机器学习",
          academic3: "创新思维与设计思维",
          academic4: "跨文化商务沟通",
          academic5: "项目管理与团队协作",
        },
        itinerary: {
          item1: "专业课程学习",
          item2: "校园参观与体验",
          item3: "文化交流活动",
          item4: "团队建设活动",
          item5: "实践项目参与",
          item6: "专家讲座",
          item7: "实地考察",
          item8: "成果展示",
        },
        admission: {
          req1: "年龄：16-25岁",
          req2: "学历：高中在读或以上",
          req3: "语言：具备基础英语沟通能力",
          req4: "身体健康，适应集体生活",
          material1: "申请表",
          material2: "个人陈述",
          material3: "学术成绩单",
          material4: "推荐信（可选）",
          material5: "护照复印件",
        },
      },
      usArt: {
        title: "青少年艺术进阶课程",
        description:
          "You Nic Co 与 Mass Audubon 合作推出青少年艺术进阶课程，将艺术与自然完美结合，激发学生的创造力和想象力。通过参观自然保护区，了解动植物，学生将在经验丰富的美术教师指导下进行艺术创作。",
        sessions: {
          session1: "2025年7月1日 - 7月14日",
          session2: "2025年7月15日 - 7月28日",
          session3: "2025年8月1日 - 8月14日",
        },
        deadline: "2025年6月15日",
        highlights: {
          highlight1: "资深美术教师授课，培养学生正确的绘画习惯",
          highlight2: "与来自世界各地的学生一起学习，锻炼英语口语能力",
          highlight3: "精心设计的课程，开拓学生的创造力和想象力",
          highlight4: "走访全美顶尖艺术院校，感受名校艺术氛围",
          highlight5: "7×24 小时安全防护，保证学生课程期间的个人安全",
        },
        academics: {
          academic1: "艺术基础理论与技法",
          academic2: "自然观察与写生技巧",
          academic3: "创意思维培养",
          academic4: "艺术史与文化背景",
          academic5: "作品集制作指导",
        },
        itinerary: {
          item1: "艺术基础课程学习",
          item2: "自然保护区参观写生",
          item3: "艺术博物馆参观",
          item4: "与当地艺术家交流",
          item5: "创作实践项目",
          item6: "艺术院校参访",
          item7: "作品展示准备",
          item8: "结业作品展览",
        },
        admission: {
          req1: "年龄：14-18岁",
          req2: "学历：初中在读或以上",
          req3: "语言：具备基础英语沟通能力",
          req4: "对艺术有浓厚兴趣",
          material1: "申请表",
          material2: "个人作品集（可选）",
          material3: "学术成绩单",
          material4: "推荐信（可选）",
          material5: "护照复印件",
        },
      },
      types: {
        innovation: "创意营理",
        stem: "STEM与科学创新",
        academic: "学术考察",
        cultural: "文化体验",
      },
    },
  },
}

// Initialize i18n
if (!i18n.isInitialized) {
  i18n.use(initReactI18next).init({
    resources,
    lng: "zh", // default language
    fallbackLng: "en",
    debug: false,

    // have a common namespace used around the full app
    defaultNS: "common",
    ns: ["common", "programs"],

    keySeparator: ".",
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
  })
}

export default i18n
