// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(EDITOR)
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts        Post[]
  pages        Page[]
  media        Media[]
  programs     Program[]
  sessions     Session[]
  testimonials Testimonial[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Content management
model Post {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String?
  status      PostStatus  @default(DRAFT)
  language    String      @default("zh")
  featuredImage String?
  seoTitle    String?
  seoDescription String?
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  authorId    String

  // Relations
  author      User        @relation(fields: [authorId], references: [id])
  categories  PostCategory[]
  tags        PostTag[]
  translations PostTranslation[]

  @@map("posts")
}

model PostTranslation {
  id          String @id @default(cuid())
  postId      String
  language    String
  title       String
  content     String
  excerpt     String?
  seoTitle    String?
  seoDescription String?

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([postId, language])
  @@map("post_translations")
}

model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  language    String   @default("zh")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  posts PostCategory[]
  translations CategoryTranslation[]

  @@map("categories")
}

model CategoryTranslation {
  id          String @id @default(cuid())
  categoryId  String
  language    String
  name        String
  description String?

  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([categoryId, language])
  @@map("category_translations")
}

model Tag {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  language  String   @default("zh")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  posts PostTag[]
  translations TagTranslation[]

  @@map("tags")
}

model TagTranslation {
  id       String @id @default(cuid())
  tagId    String
  language String
  name     String

  tag Tag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([tagId, language])
  @@map("tag_translations")
}

model PostCategory {
  postId     String
  categoryId String

  post     Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([postId, categoryId])
  @@map("post_categories")
}

model PostTag {
  postId String
  tagId  String

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([postId, tagId])
  @@map("post_tags")
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Page management for static content
model Page {
  id          String     @id @default(cuid())
  title       String
  slug        String     @unique
  content     String
  status      PageStatus @default(DRAFT)
  language    String     @default("zh")
  template    String?
  seoTitle    String?
  seoDescription String?
  publishedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  authorId    String

  author       User             @relation(fields: [authorId], references: [id])
  translations PageTranslation[]

  @@map("pages")
}

model PageTranslation {
  id          String @id @default(cuid())
  pageId      String
  language    String
  title       String
  content     String
  seoTitle    String?
  seoDescription String?

  page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, language])
  @@map("page_translations")
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Media management
model Media {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  alt         String?
  caption     String?
  uploadedBy  String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  uploader     User          @relation(fields: [uploadedBy], references: [id])
  testimonials Testimonial[]

  @@map("media")
}

// Website settings and configuration
model Setting {
  id        String   @id @default(cuid())
  key       String
  value     String
  type      SettingType @default(TEXT)
  language  String   @default("zh")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  translations SettingTranslation[]

  @@unique([key, language])
  @@map("settings")
}

model SettingTranslation {
  id        String @id @default(cuid())
  settingId String
  language  String
  value     String

  setting Setting @relation(fields: [settingId], references: [id], onDelete: Cascade)

  @@unique([settingId, language])
  @@map("setting_translations")
}

enum SettingType {
  TEXT
  TEXTAREA
  HTML
  JSON
  BOOLEAN
  NUMBER
  URL
  EMAIL
}

// Program management for study abroad programs
model Program {
  id          String        @id @default(cuid())
  title       String
  slug        String        @unique
  description String
  content     String
  status      ProgramStatus @default(DRAFT)
  language    String        @default("zh")
  country     String
  city        String
  duration    String
  price       Float?
  currency    String        @default("CNY")
  maxStudents Int?
  minAge      Int?
  maxAge      Int?
  startDate   DateTime?
  endDate     DateTime?
  deadline    DateTime?
  featuredImage String?
  gallery     String? // JSON string array
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  materials   String? // JSON string array
  seoTitle    String?
  seoDescription String?
  publishedAt DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  authorId    String

  author       User                @relation(fields: [authorId], references: [id])
  translations ProgramTranslation[]
  applications Application[]

  @@map("programs")
}

model ProgramTranslation {
  id          String   @id @default(cuid())
  programId   String
  language    String
  title       String
  description String
  content     String
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  materials   String? // JSON string array
  seoTitle    String?
  seoDescription String?

  program Program @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@unique([programId, language])
  @@map("program_translations")
}

enum ProgramStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  FULL
}

// Application management
model Application {
  id          String            @id @default(cuid())
  programId   String
  studentName String
  studentEmail String
  studentPhone String?
  studentAge  Int?
  parentName  String?
  parentEmail String?
  parentPhone String?
  status      ApplicationStatus @default(PENDING)
  notes       String?
  submittedAt DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  program Program @relation(fields: [programId], references: [id])

  @@map("applications")
}

enum ApplicationStatus {
  PENDING
  APPROVED
  REJECTED
  WAITLIST
}

// Newsletter subscriptions
model Newsletter {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  language    String   @default("zh")
  isActive    Boolean  @default(true)
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletters")
}

// Testimonials management
model Testimonial {
  id          String              @id @default(cuid())
  content     String
  author      String
  role        String
  program     String
  status      TestimonialStatus   @default(PUBLISHED)
  language    String              @default("zh")
  imageId     String?
  order       Int                 @default(0)
  publishedAt DateTime?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  authorId    String

  // Relations
  authorUser   User                    @relation(fields: [authorId], references: [id])
  image        Media?                  @relation(fields: [imageId], references: [id])
  translations TestimonialTranslation[]

  @@map("testimonials")
}

model TestimonialTranslation {
  id            String @id @default(cuid())
  testimonialId String
  language      String
  content       String
  author        String
  role          String
  program       String

  testimonial Testimonial @relation(fields: [testimonialId], references: [id], onDelete: Cascade)

  @@unique([testimonialId, language])
  @@map("testimonial_translations")
}

enum TestimonialStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}
