# CMS功能测试指南

## 问题解决总结

### 🔍 发现的问题
1. **组件使用错误的key**：很多组件使用的是i18n的key格式（如`hero.explore`），而不是CMS数据库中的key格式（如`hero_explore`）
2. **组件没有优先使用CMS内容**：组件的`getText`函数没有优先从数据库获取内容

### ✅ 已修复的组件
1. **ExploreSection组件**：
   - 修复了key格式（`hero.explore` → `hero_explore`）
   - 添加了CMS内容优先级

2. **Navbar组件**：
   - 修复了所有导航链接的key格式
   - 添加了useContent hook集成

3. **Footer组件**：
   - 修复了所有页脚内容的key格式
   - 更新了导航链接key

4. **Banner组件**：
   - 已经正确使用CMS key格式

5. **ProgramShowcase组件**：
   - 已经正确集成CMS内容

### 🧪 测试步骤

#### 1. 测试导航栏内容管理
1. 访问 http://localhost:3000/admin/content
2. 找到"导航栏"分组
3. 修改"nav_home"字段，比如改为"主页"
4. 保存更改
5. 访问 http://localhost:3000 查看导航栏是否更新

#### 2. 测试Banner轮播图内容管理
1. 在CMS中找到"Banner轮播图"分组
2. 修改"banner_1_title"字段
3. 保存更改
4. 刷新首页查看轮播图是否更新

#### 3. 测试首页英雄区域
1. 在CMS中找到"首页英雄区域"分组
2. 修改"hero_explore"、"hero_learn"、"hero_grow"字段
3. 保存更改
4. 查看首页"探索·学习·成长"部分是否更新

#### 4. 测试页脚内容
1. 在CMS中找到"页脚信息"分组
2. 修改任意页脚字段
3. 保存更改
4. 查看页脚是否更新

#### 5. 测试双语功能
1. 在CMS中切换到English
2. 修改英文内容
3. 保存更改
4. 在前端切换语言验证显示

### 📊 API测试命令

```bash
# 测试中文内容API
curl "http://localhost:3000/api/content?language=zh" | jq '.content.nav_home'

# 测试英文内容API
curl "http://localhost:3000/api/content?language=en" | jq '.content.nav_home'

# 测试保存功能
curl -X PUT "http://localhost:3000/api/admin/content" \
  -H "Content-Type: application/json" \
  -d '{"language":"zh","settings":{"nav_home":"测试主页"}}'
```

### 🎯 预期结果
- ✅ CMS管理界面可以正常编辑和保存内容
- ✅ 前端网站实时显示CMS中的内容更改
- ✅ 中英文内容独立管理，互不影响
- ✅ 所有组件都能正确从CMS获取内容

### 🔧 如果仍有问题

1. **检查浏览器控制台**：查看是否有JavaScript错误
2. **检查网络请求**：确认API请求是否成功
3. **清除浏览器缓存**：强制刷新页面
4. **重启开发服务器**：确保所有更改生效

### 📝 下一步优化建议

1. **添加内容预览功能**：在CMS中预览更改效果
2. **添加内容版本控制**：跟踪内容更改历史
3. **添加图片上传功能**：管理Banner和其他图片内容
4. **添加内容验证**：确保必填字段不为空
5. **添加批量操作**：一次性更新多个字段
