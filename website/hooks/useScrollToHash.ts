"use client"

import { useEffect } from "react"
import { useRouter } from "next/router"

export const useScrollToHash = () => {
  const router = useRouter()

  useEffect(() => {
    const handleRouteChange = (url: string) => {
      const hash = url.split("#")[1]
      if (hash) {
        setTimeout(() => {
          const element = document.getElementById(hash)
          if (element) {
            element.scrollIntoView({ behavior: "smooth" })
          }
        }, 100)
      }
    }

    router.events.on("routeChangeComplete", handleRouteChange)

    // If the initial URL contains a hash, scroll to it
    if (router.asPath.includes("#")) {
      handleRouteChange(router.asPath)
    }

    return () => {
      router.events.off("routeChangeComplete", handleRouteChange)
    }
  }, [router])
}
