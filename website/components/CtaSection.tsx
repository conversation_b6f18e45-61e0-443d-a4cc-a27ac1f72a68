import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

const CtaSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-black mb-4">Still Have Questions?</h2>
        <p className="text-xl md:text-2xl text-gray-800 max-w-3xl mx-auto mb-8 leading-relaxed">
          If you couldn't find the answer to your question, don't hesitate to reach out to us directly. Our advisors are
          here to help you with any inquiries you may have.
        </p>
        <Link href="/contact">
          <Button size="lg" className="bg-[#B5B568] hover:bg-[#B5B568]/90 text-white px-12 py-3 text-lg rounded-full">
            Ask An Advisor
          </Button>
        </Link>
      </div>
    </section>
  )
}

export default CtaSection
