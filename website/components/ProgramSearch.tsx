"use client"

import { useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"

const ProgramSearch = () => {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedCountries, setSelectedCountries] = useState<string[]>([])
  const [selectedGrades, setSelectedGrades] = useState<string[]>([])

  const programTypes = ["语言强化", "STEM与科学创新", "商务与艺术类", "学术拓展"]

  const countries = ["新加坡", "马来西亚", "英国", "美国", "日本"]

  const gradeOptions = ["小学", "初中", "高中"]

  const handleTypeChange = (type: string) => {
    setSelectedTypes((prev) => (prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]))
  }

  const handleCountryChange = (country: string) => {
    setSelectedCountries((prev) => (prev.includes(country) ? prev.filter((c) => c !== country) : [...prev, country]))
  }

  const handleGradeChange = (grade: string) => {
    setSelectedGrades((prev) => (prev.includes(grade) ? prev.filter((g) => g !== grade) : [...prev, grade]))
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <h2 className="text-lg font-bold mb-4">筛选项目</h2>

      {/* 项目类型 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">项目类型</h3>
        <div className="space-y-1.5">
          {programTypes.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${type}`}
                checked={selectedTypes.includes(type)}
                onCheckedChange={() => handleTypeChange(type)}
              />
              <label htmlFor={`type-${type}`} className="text-xs cursor-pointer">
                {type}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* 国家 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">国家</h3>
        <div className="space-y-1.5">
          {countries.map((country) => (
            <div key={country} className="flex items-center space-x-2">
              <Checkbox
                id={`country-${country}`}
                checked={selectedCountries.includes(country)}
                onCheckedChange={() => handleCountryChange(country)}
              />
              <label htmlFor={`country-${country}`} className="text-xs cursor-pointer">
                {country}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* 年级水平 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">年级水平</h3>
        <div className="space-y-1.5">
          {gradeOptions.map((grade) => (
            <div key={grade} className="flex items-center space-x-2">
              <Checkbox
                id={`grade-${grade}`}
                checked={selectedGrades.includes(grade)}
                onCheckedChange={() => handleGradeChange(grade)}
              />
              <label htmlFor={`grade-${grade}`} className="text-xs cursor-pointer">
                {grade}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProgramSearch
