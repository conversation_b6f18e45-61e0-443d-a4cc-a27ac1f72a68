"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { I18nextProvider } from "react-i18next"
import i18n from "@/lib/i18n"

interface I18nProviderProps {
  children: React.ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initializeI18n = async () => {
      try {
        // Wait for i18n to be ready
        if (!i18n.isInitialized) {
          await i18n.init()
        }

        // Auto-detect language on first visit
        const savedLang = localStorage.getItem("younicko-lang")
        const browserLang = navigator.language.startsWith("zh") ? "zh" : "en"
        const targetLang = savedLang || browserLang

        await i18n.changeLanguage(targetLang)

        if (!savedLang) {
          localStorage.setItem("younicko-lang", targetLang)
        }

        setIsInitialized(true)
      } catch (error) {
        console.error("Failed to initialize i18n:", error)
        setIsInitialized(true) // Still render even if there's an error
      }
    }

    initializeI18n()
  }, [])

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
}
