import type React from "react"
import Image from "next/image"

interface ProgramHeroProps {
  title: string
  backgroundImage: string
}

const ProgramHero: React.FC<ProgramHeroProps> = ({ title, backgroundImage }) => {
  return (
    <div className="relative h-[400px] w-full overflow-hidden">
      <Image
        src={backgroundImage || "/placeholder.svg"}
        alt={title}
        fill
        className="object-cover object-center"
        sizes="100vw"
      />
      <div className="absolute inset-0 bg-black/40" />
      <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4 md:px-8 max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">{title}</h1>
      </div>
    </div>
  )
}

export default ProgramHero
