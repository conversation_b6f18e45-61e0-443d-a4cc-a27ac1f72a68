"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu, X, Globe, Search } from "lucide-react"

interface NavLink {
  href: string
  label: {
    en: string
    zh: string
  }
}

interface NavMenuProps {
  navLinks: NavLink[]
}

export function NavMenu({ navLinks }: NavMenuProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [language, setLanguage] = useState<"en" | "zh">("en")
  const pathname = usePathname()

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === "en" ? "zh" : "en"))
  }

  const isActive = (path: string) => {
    return pathname === path
  }

  return (
    <>
      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center justify-center flex-1 mx-8">
        <div className="flex space-x-8">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`text-base transition-colors ${
                isActive(link.href) ? "text-[#00A0E9] font-medium" : "text-gray-300 hover:text-white"
              }`}
            >
              {link.label[language]}
            </Link>
          ))}
        </div>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center space-x-4">
        <button
          onClick={toggleLanguage}
          className="text-gray-300 hover:text-white transition-colors flex items-center px-2 py-1 rounded-md border border-gray-700"
          aria-label={language === "en" ? "Switch to Chinese" : "Switch to English"}
        >
          <Globe size={20} className="mr-1" />
          <span className="text-sm font-medium">{language === "en" ? "中文" : "EN"}</span>
        </button>
        <button className="text-gray-300 hover:text-white transition-colors">
          <Search size={20} />
        </button>
        <button
          className="md:hidden text-gray-300 hover:text-white transition-colors"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="absolute top-14 left-0 right-0 bg-gray-900 border-b border-gray-800 py-2 px-4 md:hidden">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`block py-2 text-base transition-colors ${
                isActive(link.href) ? "text-[#00A0E9] font-medium" : "text-gray-300 hover:text-white"
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              {link.label[language]}
            </Link>
          ))}
        </div>
      )}
    </>
  )
}
