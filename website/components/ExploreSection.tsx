"use client"

import type React from "react"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"

const ExploreSection: React.FC = () => {
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  return (
    <section className="bg-white py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-2">
            <span style={{ color: "#00A0E9" }}>{getText("hero_explore", "探索.")}</span>{" "}
            <span style={{ color: "#4CAF50" }}>{getText("hero_learn", "学习.")}</span>{" "}
            <span style={{ color: "#FF9800" }}>{getText("hero_grow", "成长.")}</span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-600">
            {getText("hero_subtitle", "您的终身学习之旅等待着您")}
          </p>
        </div>
      </div>
    </section>
  )
}

export default ExploreSection
