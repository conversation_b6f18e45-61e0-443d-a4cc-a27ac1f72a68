"use client"

import { useState } from "react"
import Link from "next/link"
import { ChevronLeft, ChevronRight, Clock } from "lucide-react"
import { Button } from "./ui/button"

interface ChinaProgram {
  id: string
  title: string
  type: string[]
  city: string
  description: string
  image: string
  deadline?: string
}

const chinaPrograms: ChinaProgram[] = [
  {
    id: "beijing-cultural-immersion",
    title: "北京文化沉浸式体验营",
    type: ["文化体验", "学术拓展"],
    city: "北京",
    description: "深度探索北京的历史文化，参观故宫、长城等世界文化遗产，体验传统文化工坊，感受古都魅力...",
    image: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d?q=80&w=2070&fit=crop",
    deadline: "2025年6月15日",
  },
  {
    id: "shanghai-business-innovation",
    title: "上海商业创新与科技探索",
    type: ["商务与艺术类", "STEM与科学创新"],
    city: "上海",
    description: "走进上海国际金融中心，参访知名企业，了解中国商业模式创新，体验前沿科技发展...",
    image: "https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?q=80&w=2070&fit=crop",
    deadline: "2025年6月20日",
  },
  {
    id: "shenzhen-tech-valley",
    title: "深圳科技硅谷创新之旅",
    type: ["STEM与科学创新", "学术拓展"],
    city: "深圳",
    description: "探访中国硅谷深圳，参观腾讯、华为等科技巨头，了解中国科技创新发展历程...",
    image: "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
    deadline: "2025年6月25日",
  },
  {
    id: "chengdu-panda-culture",
    title: "成都熊猫文化与川蜀文明",
    type: ["文化体验", "语言强化"],
    city: "成都",
    description: "走进熊猫故乡成都，探索川蜀文化，品味地道川菜，学习中文，感受西南地区独特魅力...",
    image: "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?q=80&w=2070&fit=crop",
    deadline: "2025年6月30日",
  },
  {
    id: "xian-ancient-capital",
    title: "西安古都历史文化探索",
    type: ["文化体验", "学术拓展"],
    city: "西安",
    description: "探索十三朝古都西安，参观兵马俑、大雁塔等历史遗迹，深入了解中华文明发源地...",
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=2070&fit=crop",
    deadline: "2025年7月5日",
  },
  {
    id: "beijing-language-intensive",
    title: "北京中文语言强化课程",
    type: ["语言强化", "文化体验"],
    city: "北京",
    description: "在北京语言大学进行中文强化学习，结合文化体验活动，快速提升中文水平...",
    image: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
    deadline: "2025年7月10日",
  },
]

const ITEMS_PER_PAGE = 6

const ChinaProgramList = () => {
  const [currentPage, setCurrentPage] = useState(1)

  const totalPages = Math.ceil(chinaPrograms.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const currentPrograms = chinaPrograms.slice(startIndex, endIndex)

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {currentPrograms.map((program) => (
          <Link
            key={program.id}
            href={`/study-china/${program.id}`}
            className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full"
          >
            <div className="relative h-48">
              <img
                src={program.image || "/placeholder.svg"}
                alt={program.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-4 flex-grow flex flex-col">
              {/* 标题 - 限制为2行 */}
              <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight min-h-[3.5rem]">
                {program.title}
              </h3>

              {/* 项目类型 */}
              <div className="flex flex-wrap gap-1 mb-2">
                <span className="text-xs text-gray-600">类型:</span>
                {program.type.map((type, index) => (
                  <span key={index} className="text-xs text-blue-600">
                    {type}
                    {index < program.type.length - 1 ? ", " : ""}
                  </span>
                ))}
              </div>

              {/* 城市 */}
              <div className="mb-3">
                <span className="text-xs text-gray-600">城市: </span>
                <span className="text-xs text-blue-600">{program.city}</span>
              </div>

              {/* 截止日期 - 放在底部 */}
              <div className="mt-auto flex items-center text-red-600">
                <Clock className="h-3 w-3 mr-1" />
                <span className="text-xs font-medium">截止日期: {program.deadline}</span>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-center space-x-2 mt-8">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setCurrentPage((page) => Math.max(1, page - 1))}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <span className="text-sm text-gray-600">上一页</span>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentPage(page)}
            className="min-w-[32px]"
          >
            {page}
          </Button>
        ))}

        <span className="text-sm text-gray-600">下一页</span>

        <Button
          variant="outline"
          size="icon"
          onClick={() => setCurrentPage((page) => Math.min(totalPages, page + 1))}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

export default ChinaProgramList
