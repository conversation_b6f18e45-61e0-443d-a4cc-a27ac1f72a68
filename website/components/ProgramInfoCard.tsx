"use client"

import type React from "react"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, GraduationCap } from "lucide-react"

interface ProgramInfoCardProps {
  type: string[]
  duration: string
  city: string
  gradeLevel: string[]
  sessions: string[]
  deadline: string
}

const ProgramInfoCard: React.FC<ProgramInfoCardProps> = ({ type, duration, city, gradeLevel, sessions, deadline }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 space-y-4">
      <h3 className="text-lg font-semibold mb-4">项目信息</h3>

      {/* 第一行：项目类型 + 时长 */}
      <div className="flex flex-wrap items-center gap-6">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-600">项目类型:</span>
          <div className="flex gap-2">
            {type.map((t, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {t}
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-600">时长:</span>
          <span className="text-sm font-medium text-gray-900">{duration}</span>
        </div>
      </div>

      {/* 第二行：城市 + 年级 */}
      <div className="flex flex-wrap items-center gap-6">
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-600">城市:</span>
          <span className="text-sm font-medium text-gray-900">{city}</span>
        </div>

        <div className="flex items-center gap-2">
          <GraduationCap className="w-4 h-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-600">年级:</span>
          <div className="flex gap-2">
            {gradeLevel.map((grade, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {grade}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 第三行：营期 */}
      <div className="flex items-center gap-2">
        <Calendar className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-600">营期:</span>
        <div className="flex flex-wrap gap-2">
          {sessions.map((session, index) => (
            <Badge key={index} variant="default" className="text-xs bg-blue-100 text-blue-800">
              {session}
            </Badge>
          ))}
        </div>
      </div>

      {/* 第四行：截止日期 */}
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4 text-red-600" />
        <span className="text-sm font-medium text-gray-600">截止日期:</span>
        <Badge variant="destructive" className="text-xs">
          {deadline}
        </Badge>
      </div>
    </div>
  )
}

export default ProgramInfoCard
