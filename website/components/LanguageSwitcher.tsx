"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Globe } from "lucide-react"

export function LanguageSwitcher() {
  const { i18n } = useTranslation()
  const [currentLang, setCurrentLang] = useState("zh")
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    const initLanguage = async () => {
      try {
        // Wait for i18n to be ready
        if (i18n && typeof i18n.changeLanguage === "function") {
          const savedLang = localStorage.getItem("younicko-lang")
          const browserLang = navigator.language.startsWith("zh") ? "zh" : "en"
          const initialLang = savedLang || browserLang

          setCurrentLang(initialLang)
          await i18n.changeLanguage(initialLang)
          setIsReady(true)
        }
      } catch (error) {
        console.error("Error initializing language:", error)
        setIsReady(true)
      }
    }

    initLanguage()
  }, [i18n])

  const changeLanguage = async (lng: string) => {
    try {
      if (i18n && typeof i18n.changeLanguage === "function") {
        setCurrentLang(lng)
        await i18n.changeLanguage(lng)
        localStorage.setItem("younicko-lang", lng)
      }
    } catch (error) {
      console.error("Error changing language:", error)
    }
  }

  if (!isReady) {
    return (
      <div className="flex items-center px-2 py-1">
        <Globe size={18} className="mr-1 text-gray-400" />
        <span className="text-sm font-medium text-gray-400">...</span>
      </div>
    )
  }

  return (
    <button
      onClick={() => changeLanguage(currentLang === "en" ? "zh" : "en")}
      className="text-black hover:text-[#00A0E9] transition-colors flex items-center px-2 py-1 rounded-md"
      aria-label={currentLang === "en" ? "Switch to Chinese" : "Switch to English"}
    >
      <Globe size={18} className="mr-1" />
      <span className="text-sm font-medium">{currentLang === "en" ? "中文" : "EN"}</span>
    </button>
  )
}
