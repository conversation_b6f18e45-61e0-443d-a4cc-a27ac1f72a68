"use client"

import type React from "react"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

interface ProgramTabsCardProps {
  programId: string
}

const ProgramTabsCard: React.FC<ProgramTabsCardProps> = ({ programId }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <Tabs defaultValue="highlights" className="w-full">
        <TabsList className="w-full grid grid-cols-4">
          <TabsTrigger value="highlights">亮点</TabsTrigger>
          <TabsTrigger value="academics">学术</TabsTrigger>
          <TabsTrigger value="itinerary">行程</TabsTrigger>
          <TabsTrigger value="admission">申请</TabsTrigger>
        </TabsList>

        <TabsContent value="highlights" className="mt-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">项目亮点</h3>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>深度体验中国传统文化与现代创新的完美融合</li>
              <li>参访知名企业，了解中国数字经济发展模式</li>
              <li>与当地学生交流，提升跨文化沟通能力</li>
              <li>专业导师指导，获得学术认证</li>
              <li>沉浸式语言环境，快速提升中文水平</li>
            </ul>
          </div>
        </TabsContent>

        <TabsContent value="academics" className="mt-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">学术内容</h3>
            <div className="space-y-3 text-gray-700">
              <p>本项目结合理论学习与实践体验，涵盖以下学术模块：</p>
              <ul className="list-disc pl-5 space-y-2">
                <li>中国经济发展史与现状分析</li>
                <li>数字化转型与创新案例研究</li>
                <li>中华文化传承与现代化进程</li>
                <li>跨文化商务沟通技巧</li>
                <li>项目管理与团队协作</li>
              </ul>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="itinerary" className="mt-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">行程安排</h3>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold">第一周：文化探索</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
                  <li>成都市区文化景点参观</li>
                  <li>传统工艺体验工坊</li>
                  <li>当地大学校园参访</li>
                  <li>文化交流活动</li>
                </ul>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold">第二周：创新实践</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
                  <li>科技企业参访</li>
                  <li>创新项目实践</li>
                  <li>行业专家讲座</li>
                  <li>成果展示与总结</li>
                </ul>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="admission" className="mt-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">申请要求</h3>
            <div className="space-y-3 text-gray-700">
              <div>
                <h4 className="font-semibold mb-2">基本要求：</h4>
                <ul className="list-disc pl-5 space-y-1">
                  <li>年龄：16-25岁</li>
                  <li>学历：高中在读或以上</li>
                  <li>语言：具备基础中文沟通能力</li>
                  <li>身体健康，适应集体生活</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">申请材料：</h4>
                <ul className="list-disc pl-5 space-y-1">
                  <li>申请表</li>
                  <li>个人陈述</li>
                  <li>学术成绩单</li>
                  <li>推荐信（可选）</li>
                  <li>护照复印件</li>
                </ul>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ProgramTabsCard
