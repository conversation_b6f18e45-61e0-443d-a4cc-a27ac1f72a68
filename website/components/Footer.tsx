"use client"

import Link from "next/link"
import { Linkedin, Instagram } from "lucide-react"
import Image from "next/image"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"

const Footer = () => {
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  const navLinks = [
    { href: "/", labelKey: "footer_nav_home", fallback: "Home" },
    { href: "/programs", labelKey: "footer_nav_world_study", fallback: "World Study Tour" },
    { href: "/study-china", labelKey: "footer_nav_china_study", fallback: "China Study Tour" },
    { href: "/blog", labelKey: "footer_nav_blog", fallback: "Blog" },
    { href: "/about", labelKey: "footer_nav_about", fallback: "About EdGoing" },
    { href: "/faq", labelKey: "footer_nav_faq", fallback: "FAQ" },
    { href: "/contact", labelKey: "footer_nav_contact", fallback: "Let's Plan" },
  ]

  return (
    <footer className="bg-gray-50 text-gray-700 py-12">
      <div className="container mx-auto px-6 md:px-8 lg:px-16 xl:px-24">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 lg:gap-16 xl:gap-20">
          {/* Navigation Column */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{getText("footer_navigation", "NAVIGATION")}</h3>
            <div className="space-y-3">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="block text-gray-600 hover:text-blue-600 transition-colors"
                >
                  {getText(link.labelKey, link.fallback)}
                </Link>
              ))}
            </div>
          </div>

          {/* Contact Us Column */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{getText("footer_contact_us", "CONTACT US")}</h3>
            <div className="space-y-3">
              <div>
                <span className="text-gray-600">{getText("footer_call_us", "Call Us")}: </span>
                <a href="tel:4001153558" className="text-blue-600 hover:underline">
                  4001153558
                </a>
              </div>
              <div>
                <span className="text-gray-600">{getText("footer_email", "Email")}: </span>
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-gray-600">{getText("footer_address", "Address")}: </span>
                  <span className="text-gray-600">{getText("footer_locations", "Shanghai | Singapore")}</span>
                </div>
                <div className="text-gray-600">
                  <div>
                    {getText("footer_shanghai", "Shanghai")}:{" "}
                    {getText("footer_shanghai_address", "18F, Tower B, 838 South Huangpi Road")}
                  </div>
                  <div>{getText("footer_shanghai_district", "Huangpu District, Shanghai, 200025")}</div>
                  <div className="mt-1">
                    {getText("footer_singapore", "Singapore")}:{" "}
                    {getText("footer_singapore_address", "9 Kelantan Lane #06-01")}
                  </div>
                  <div>{getText("footer_singapore_postal", "Singapore 208628")}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Follow Us Column */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-gray-900">{getText("footer_follow_us", "FOLLOW US")}</h3>
            <div className="space-y-6">
              <p className="text-gray-600">
                {getText("footer_follow_description", "Follow us on social media for updates and educational insights")}
              </p>

              <div className="flex gap-4">
                <a href="#linkedin" className="text-gray-500 hover:text-blue-600 transition-colors">
                  <Linkedin className="w-8 h-8" />
                </a>
                <a href="#instagram" className="text-gray-500 hover:text-blue-600 transition-colors">
                  <Instagram className="w-8 h-8" />
                </a>
                <a href="#wechat" className="text-gray-500 hover:text-blue-600 transition-colors">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/%E5%85%AC%E4%BC%97%E5%8F%B7%20logo-AWFVKQOjRtc3zsxuok8A2MU62foAAK.png"
                    alt="WeChat"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </a>
                <a href="#custom" className="text-gray-500 hover:text-blue-600 transition-colors">
                  <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">M</span>
                  </div>
                </a>
              </div>

              <div className="mt-8">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-v7yCFSclS3MD2wJEDJ30n37p2hVY6r.png"
                  alt="EdGoing Logo"
                  width={120}
                  height={40}
                  className="h-10 w-auto"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 mt-12 pt-8 text-center text-gray-500">
          <p>{getText("footer_copyright", "2025 EdGoing. All rights reserved.")}</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
