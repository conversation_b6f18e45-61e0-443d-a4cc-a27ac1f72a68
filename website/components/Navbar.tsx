"use client"

import { useState } from "react"
import { Menu, X } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"
import { LanguageSwitcher } from "./LanguageSwitcher"

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const isActive = (path: string) => {
    return pathname === path
  }

  const navLinks = [
    { href: "/", key: "nav_home", fallback: "首页" },
    { href: "/programs", key: "nav_programs", fallback: "游学国际" },
    { href: "/study-china", key: "nav_study_china", fallback: "游学中国" },
    { href: "/about", key: "nav_about", fallback: "关于EdGoing" },
    { href: "/blog", key: "nav_blog", fallback: "博客" },
    { href: "/contact", key: "nav_contact", fallback: "开始项目" },
  ]

  const getNavText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  return (
    <nav className="bg-white h-16 flex items-center justify-between px-4 lg:px-8 border-b border-gray-200">
      <Link href="/" className="flex items-center">
        <div className="h-12 w-48 relative">
          <Image
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-frhoJE43UY4KynNNmUJpQlQZwk8g15.png"
            alt="YOUNIKCO - EXPLORE. LEARN. GROW"
            fill
            className="object-contain"
            priority
          />
        </div>
      </Link>

      {/* Desktop Navigation */}
      <div className="hidden lg:flex items-center justify-center flex-1 mx-4">
        <div className="flex items-center justify-between w-full max-w-4xl">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`transition-all duration-200 text-sm ${
                isActive(link.href) ? "text-[#00A0E9] font-medium" : "text-black hover:text-[#00A0E9]"
              } ${
                link.href === "/contact"
                  ? "px-4 py-2 rounded-full border border-black/40 hover:bg-black/5 hover:border-black"
                  : "px-3 py-2 hover:bg-black/5 rounded-md"
              }`}
            >
              <span className="whitespace-nowrap">{getNavText(link.key, link.fallback)}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center space-x-4">
        <LanguageSwitcher />
        <button
          className="lg:hidden text-gray-700 hover:text-[#00A0E9] transition-colors"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="absolute top-16 left-0 right-0 bg-white border-b border-gray-200 py-2 px-4 lg:hidden">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`block py-2 text-sm transition-colors ${
                isActive(link.href) ? "text-[#00A0E9] font-medium" : "text-gray-700 hover:text-[#00A0E9]"
              } ${link.href === "/contact" ? "mt-2 text-center border border-gray-300 rounded-full py-2" : ""}`}
              onClick={() => setIsMenuOpen(false)}
            >
              {getNavText(link.key, link.fallback)}
            </Link>
          ))}
        </div>
      )}
    </nav>
  )
}

export default Navbar
