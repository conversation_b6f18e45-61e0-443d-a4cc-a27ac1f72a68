"use client"

import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"
import Link from "next/link"
import Image from "next/image"

const ProgramShowcase = () => {
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  const programs = [
    {
      id: "china-cultural",
      title: "中国城市之美：文化体验与未来未来",
      subtitle: "中国深度之旅：文化体验与科技未来",
      image: "https://images.unsplash.com/photo-1506905925346-29e86c435425?q=80&w=2070&fit=crop",
      link: "/programs/china-cultural",
    },
    {
      id: "stem-innovation",
      title: "新加坡科学营暨理财大学学术之旅",
      subtitle: "",
      image: "https://images.unsplash.com/photo-1581362072978-14998d01fdaa?q=80&w=2070&fit=crop",
      link: "/programs/stem-innovation",
    },
    {
      id: "ai-future",
      title: "2025年新加坡STEM与AI之旅",
      subtitle: "",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2070&fit=crop",
      link: "/programs/ai-future",
    },
  ]

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {getText("program_showcase_title", "游学中国新视角：激发灵感、建立联系、实现财富的旅程。")}
          </h2>
          <Link href="/programs" className="text-blue-600 hover:text-blue-700 font-medium inline-flex items-center">
            {getText("program_showcase_link_text", "查看所有项目 →")}
          </Link>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {programs.map((program) => (
            <Link
              key={program.id}
              href={program.link}
              className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={program.image || "/placeholder.svg"}
                  alt={program.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{program.title}</h3>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>了解更多</span>
                  <span>查看详情</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProgramShowcase
