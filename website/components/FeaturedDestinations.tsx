const destinations = [
  {
    id: 1,
    title: "United States",
    image: "/placeholder.svg?height=400&width=600",
    programs: 234,
    rating: 4.8,
  },
  {
    id: 2,
    title: "United Kingdom",
    image: "/placeholder.svg?height=400&width=600",
    programs: 156,
    rating: 4.9,
  },
  {
    id: 3,
    title: "Australia",
    image: "/placeholder.svg?height=400&width=600",
    programs: 98,
    rating: 4.7,
  },
]

const FeaturedDestinations = () => {
  return (
    <section className="grid gap-12">
      <div className="text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-4">Popular Destinations</h2>
        <p className="text-text-secondary text-lg max-w-2xl mx-auto">
          Explore our most sought-after study destinations, each offering unique learning experiences and cultural
          immersion.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8">
        {destinations.map((destination) => (
          <div
            key={destination.id}
            className="group relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
          >
            <div className="aspect-[4/3] relative">
              <img
                src={destination.image || "/placeholder.svg"}
                alt={destination.title}
                className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            </div>
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              <h3 className="text-2xl font-bold mb-2">{destination.title}</h3>
              <div className="flex justify-between items-center">
                <span>{destination.programs} Programs</span>
                <span>★ {destination.rating}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}

export default FeaturedDestinations
