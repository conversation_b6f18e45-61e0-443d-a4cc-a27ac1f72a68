import { Globe, GraduationCap, Users, Briefcase } from "lucide-react"

const StudyChinaFeatures = () => {
  const features = [
    {
      icon: Globe,
      title: "沉浸式语言学习",
      description: "在真实的中文环境中提升语言能力，由经验丰富的教师系统化语言教学与生活实践相结合的学习体验。",
    },
    {
      icon: GraduationCap,
      title: "顶尖学术资源",
      description: "深度，北大等顶尖学府的学术资源，接触前沿的中国研究和学术成果。",
    },
    {
      icon: Users,
      title: "深度文化体验",
      description: "从故宫到长城，从茶艺到书法，全方位体验中华文化，参与书法、武术等传统文化工坊。",
    },
    {
      icon: Briefcase,
      title: "商业实践机会",
      description: "参访中国领先企业，了解中国商业模式与创新科技发展。",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">学在中国，赢在未来</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">探索在中国学习的独特优势，为您的未来奠基础。</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed text-sm">{feature.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default StudyChinaFeatures
