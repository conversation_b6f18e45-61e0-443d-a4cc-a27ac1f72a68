"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { ChevronLeft, ChevronRight, Clock } from 'lucide-react'
import { Button } from "./ui/button"
import { useTranslation } from "react-i18next"

interface Program {
  id: string
  title: string
  slug: string
  description: string
  country: string
  city: string
  duration?: string
  price?: number
  currency: string
  deadline?: string
  featuredImage?: string
  _count: {
    applications: number
  }
}

const ITEMS_PER_PAGE = 6

const ProgramList = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [programs, setPrograms] = useState<Program[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [totalPages, setTotalPages] = useState(1)
  const { i18n } = useTranslation()

  useEffect(() => {
    fetchPrograms()
  }, [currentPage, i18n.language])

  const fetchPrograms = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/programs?page=${currentPage}&limit=${ITEMS_PER_PAGE}&language=${i18n.language}`)
      if (response.ok) {
        const data = await response.json()
        setPrograms(data.programs)
        setTotalPages(data.pagination.pages)
      } else {
        setError('Failed to fetch programs')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div>Loading programs...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {programs.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500">No programs available</p>
          </div>
        ) : (
          programs.map((program) => (
            <Link
              key={program.id}
              href={`/programs/${program.slug}`}
              className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full"
            >
              <div className="relative h-48">
                <img
                  src={program.featuredImage || "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop"}
                  alt={program.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-4 flex-grow flex flex-col">
                {/* 标题 - 限制为2行 */}
                <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight min-h-[3.5rem]">
                  {program.title}
                </h3>

                {/* 描述 */}
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {program.description}
                </p>

                {/* 国家和城市 */}
                <div className="mb-2">
                  <span className="text-xs text-gray-600">地点: </span>
                  <span className="text-xs text-blue-600">{program.country}, {program.city}</span>
                </div>

                {/* 时长和价格 */}
                {program.duration && (
                  <div className="mb-2">
                    <span className="text-xs text-gray-600">时长: </span>
                    <span className="text-xs text-blue-600">{program.duration}</span>
                  </div>
                )}

                {program.price && (
                  <div className="mb-2">
                    <span className="text-xs text-gray-600">价格: </span>
                    <span className="text-xs text-blue-600">{program.currency} {program.price.toLocaleString()}</span>
                  </div>
                )}

                {/* 申请数量 */}
                <div className="mb-3">
                  <span className="text-xs text-gray-600">申请人数: </span>
                  <span className="text-xs text-blue-600">{program._count.applications}</span>
                </div>

                {/* 截止日期 - 放在底部 */}
                {program.deadline && (
                  <div className="mt-auto flex items-center text-red-600">
                    <Clock className="h-3 w-3 mr-1" />
                    <span className="text-xs font-medium">截止日期: {new Date(program.deadline).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </Link>
          ))
        )}
      </div>

      {/* Pagination Controls - Only show if there are multiple pages */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 mt-8">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage((page) => Math.max(1, page - 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <span className="text-sm text-gray-600">上一页</span>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentPage(page)}
              className="min-w-[32px]"
            >
              {page}
            </Button>
          ))}

          <span className="text-sm text-gray-600">下一页</span>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage((page) => Math.min(totalPages, page + 1))}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}

export default ProgramList
