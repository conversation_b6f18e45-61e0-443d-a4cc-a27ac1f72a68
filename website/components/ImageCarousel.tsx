"use client"

import type React from "react"
import Slider from "react-slick"
import Image from "next/image"
import "slick-carousel/slick/slick.css"
import "slick-carousel/slick/slick-theme.css"

interface ImageCarouselProps {
  images: string[]
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images }) => {
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  }

  return (
    <div className="w-full max-w-4xl mx-auto my-8">
      <Slider {...settings}>
        {images.map((image, index) => (
          <div key={index} className="relative h-[400px]">
            <Image
              src={image || "/placeholder.svg"}
              alt={`Program image ${index + 1}`}
              fill
              className="object-cover rounded-lg"
            />
          </div>
        ))}
      </Slider>
    </div>
  )
}

export default ImageCarousel
