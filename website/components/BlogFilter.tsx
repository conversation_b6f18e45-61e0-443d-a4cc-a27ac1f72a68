"use client"

import { useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"

interface FilterOption {
  label: string
  options: string[]
}

const filterOptions: FilterOption[] = [
  {
    label: "Program Type",
    options: ["Academic", "Cultural", "Language", "Internship"],
  },
  {
    label: "Age Group",
    options: ["Elementary", "Middle School", "High School", "University", "Adult"],
  },
  {
    label: "Region",
    options: ["North America", "Europe", "Asia", "South America", "Africa", "Oceania"],
  },
  {
    label: "Duration",
    options: ["Summer", "Semester", "Year", "Short-term"],
  },
]

const BlogFilter = () => {
  const [filters, setFilters] = useState<Record<string, string[]>>({})

  const handleFilterChange = (category: string, option: string) => {
    setFilters((prevFilters) => {
      const categoryFilters = prevFilters[category] || []
      if (categoryFilters.includes(option)) {
        return {
          ...prevFilters,
          [category]: categoryFilters.filter((item) => item !== option),
        }
      } else {
        return {
          ...prevFilters,
          [category]: [...categoryFilters, option],
        }
      }
    })
  }

  return (
    <div className="space-y-6 bg-card p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-semibold text-card-foreground mb-4">Filter Articles</h3>
      {filterOptions.map((filterOption) => (
        <div key={filterOption.label} className="mb-4">
          <h4 className="font-medium mb-2 text-card-foreground">{filterOption.label}</h4>
          <div className="space-y-2">
            {filterOption.options.map((option) => (
              <div key={option} className="flex items-center">
                <Checkbox
                  id={option}
                  checked={filters[filterOption.label]?.includes(option) || false}
                  onCheckedChange={() => handleFilterChange(filterOption.label, option)}
                />
                <label htmlFor={option} className="ml-2 text-base text-muted-foreground">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

export default BlogFilter
