"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { useSearchParams } from "next/navigation"

const roles = ["Student", "Parent", "Teacher", "School Administrator", "Educational Consultant", "Other"]

const provinces = [
  "北京市",
  "上海市",
  "天津市",
  "重庆市",
  "河北省",
  "山西省",
  "辽宁省",
  "吉林省",
  "黑龙江省",
  "江苏省",
  "浙江省",
  "安徽省",
  "福建省",
  "江西省",
  "山东省",
  "河南省",
  "湖北省",
  "湖南省",
  "广东省",
  "海南省",
  "四川省",
  "贵州省",
  "云南省",
  "陕西省",
  "甘肃省",
  "青海省",
  "台湾省",
  "内蒙古自治区",
  "广西壮族自治区",
  "西藏自治区",
  "宁夏回族自治区",
  "新疆维吾尔自治区",
  "香港特别行政区",
  "澳门特别行政区",
]

const cities = {
  北京市: [
    "东城区",
    "西城区",
    "朝阳区",
    "丰台区",
    "石景山区",
    "海淀区",
    "门头沟区",
    "房山区",
    "通州区",
    "顺义区",
    "昌平区",
    "大兴区",
    "怀柔区",
    "平谷区",
    "密云区",
    "延庆区",
  ],
  上海市: [
    "黄浦区",
    "徐汇区",
    "长宁区",
    "静安区",
    "普陀区",
    "虹口区",
    "杨浦区",
    "闵行区",
    "宝山区",
    "嘉定区",
    "浦东新区",
    "金山区",
    "松江区",
    "青浦区",
    "奉贤区",
    "崇明区",
  ],
  广东省: [
    "广州市",
    "深圳市",
    "珠海市",
    "汕头市",
    "佛山市",
    "韶关市",
    "湛江市",
    "肇庆市",
    "江门市",
    "茂名市",
    "惠州市",
    "梅州市",
    "汕尾市",
    "河源市",
    "阳江市",
    "清远市",
    "东莞市",
    "中山市",
    "潮州市",
    "揭阳市",
    "云浮市",
  ],
  江苏省: [
    "南京市",
    "无锡市",
    "徐州市",
    "常州市",
    "苏州市",
    "南通市",
    "连云港市",
    "淮安市",
    "盐城市",
    "扬州市",
    "镇江市",
    "泰州市",
    "宿迁市",
  ],
  浙江省: [
    "杭州市",
    "宁波市",
    "温州市",
    "嘉兴市",
    "湖州市",
    "绍兴市",
    "金华市",
    "衢州市",
    "舟山市",
    "台州市",
    "丽水市",
  ],
  山西省: [
    "太原市",
    "大同市",
    "阳泉市",
    "长治市",
    "晋城市",
    "朔州市",
    "晋中市",
    "运城市",
    "忻州市",
    "临汾市",
    "吕梁市",
  ],
  新疆维吾尔自治区: [
    "乌鲁木齐市",
    "克拉玛依市",
    "吐鲁番市",
    "哈密市",
    "昌吉回族自治州",
    "博尔塔拉蒙古自治州",
    "巴音郭楞蒙古自治州",
    "阿克苏地区",
    "克孜勒苏柯尔克孜自治州",
    "喀什地区",
    "和田地区",
    "伊犁哈萨克自治州",
    "塔城地区",
    "阿勒泰地区",
    "石河子市",
    "阿拉尔市",
    "图木舒克市",
    "五家渠市",
    "北屯市",
    "铁门关市",
    "双河市",
    "可克达拉市",
    "昆玉市",
    "胡杨河市",
  ],
  内蒙古自治区: [
    "呼和浩特市",
    "包头市",
    "乌海市",
    "赤峰市",
    "通辽市",
    "鄂尔多斯市",
    "呼伦贝尔市",
    "巴彦淖尔市",
    "乌兰察布市",
    "兴安盟",
    "锡林郭勒盟",
    "阿拉善盟",
  ],
  广西壮族自治区: [
    "南宁市",
    "柳州市",
    "桂林市",
    "梧州市",
    "北海市",
    "防城港市",
    "钦州市",
    "贵港市",
    "玉林市",
    "百色市",
    "贺州市",
    "河池市",
    "来宾市",
    "崇左市",
  ],
  西藏自治区: ["拉萨市", "日喀则市", "昌都市", "林芝市", "山南市", "那曲市", "阿里地区"],
  宁夏回族自治区: ["银川市", "石嘴山市", "吴忠市", "固原市", "中卫市"],
  // Add other provinces and their cities as needed
} as const

const gradeOptions = ["Elementary", "Middle School", "High School", "University", "Professional"]
const interestOptions = [
  "Short-term Study",
  "Semester Exchange",
  "Degree Program",
  "Summer Camp",
  "Winter Camp",
  "Language Course",
  "Other",
]
const preferredDestinations = ["USA", "UK", "Canada", "Australia", "New Zealand", "Japan", "Singapore", "Other"]

const learningInterests = [
  "History & Civics",
  "STEM/Science",
  "Cultural Exploration",
  "Religion & Faith",
  "Performing Arts",
  "Language Intensive",
  "Community Engagement",
  "Sports",
  "Specialty",
  "Academic",
  "Others",
]

export default function ContactForm() {
  const [formData, setFormData] = useState({
    role: "",
    schoolName: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    grade: "",
    province: "",
    city: "",
    interests: [] as string[],
    destinations: [] as string[],
    learningInterests: [] as string[],
    message: "",
    consent: false,
  })

  const formRef = useRef<HTMLFormElement>(null)
  const searchParams = useSearchParams()

  useEffect(() => {
    if (searchParams.get("scroll") === "form" && formRef.current) {
      setTimeout(() => {
        formRef.current?.scrollIntoView({ behavior: "smooth", block: "start" })
      }, 100)
    }
  }, [searchParams])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
      ...(name === "province" ? { city: "" } : {}), // Reset city when province changes
    }))
  }

  const handleMultiSelect = (name: "interests" | "destinations" | "learningInterests", value: string) => {
    setFormData((prev) => {
      const currentValues = prev[name]
      if (currentValues.includes(value)) {
        return { ...prev, [name]: currentValues.filter((v) => v !== value) }
      } else {
        return { ...prev, [name]: [...currentValues, value] }
      }
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement form submission
    console.log(formData)
  }

  useEffect(() => {
    if (formData.province && cities[formData.province as keyof typeof cities]) {
      setFormData((prev) => ({ ...prev, city: "" }))
    }
  }, [formData.province])

  return (
    <form ref={formRef} id="contact-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto space-y-6 pt-8">
      <div className="mb-6">
        <p className="text-gray-600 space-y-2 text-center">
          <span className="block">Need help with assistance, or just have a question for us?</span>
          <span className="block">Fill out our form and we'll respond within 2 business days.</span>
          <span className="block">
            Or Call Us @ <span className="text-gray-600">400-400-400</span>
          </span>
        </p>
      </div>
      <div className="space-y-6">
        {/* Role Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Your Role*</label>
          <Select value={formData.role} onValueChange={(value) => setFormData((prev) => ({ ...prev, role: value }))}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select your role" />
            </SelectTrigger>
            <SelectContent>
              {roles.map((role) => (
                <SelectItem key={role} value={role}>
                  {role}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium mb-2">
              First Name*
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter your first name"
              required
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium mb-2">
              Last Name*
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter your last name"
              required
            />
          </div>
        </div>

        {/* Contact Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-2">
              Email*
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter your email address"
              required
            />
          </div>
          <div>
            <label htmlFor="phone" className="block text-sm font-medium mb-2">
              Phone Number*
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter your phone number"
              required
            />
          </div>
        </div>

        {/* School Name */}
        <div>
          <label htmlFor="schoolName" className="block text-sm font-medium mb-2">
            School Name
          </label>
          <Input
            type="text"
            id="schoolName"
            name="schoolName"
            value={formData.schoolName}
            onChange={handleChange}
            className="w-full"
            placeholder="Enter your school name (optional)"
          />
        </div>

        {/* Grade/Education Level */}
        <div>
          <label className="block text-sm font-medium mb-2">Grade Level*</label>
          <Select value={formData.grade} onValueChange={(value) => setFormData((prev) => ({ ...prev, grade: value }))}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select your grade level" />
            </SelectTrigger>
            <SelectContent>
              {gradeOptions.map((grade) => (
                <SelectItem key={grade} value={grade.toLowerCase().replace(" ", "_")}>
                  {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Province and City Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">Province</label>
            <Select
              value={formData.province}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, province: value, city: "" }))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select your province" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px]">
                {provinces.map((province) => (
                  <SelectItem key={province} value={province}>
                    {province}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">City</label>
            <Select
              value={formData.city}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, city: value }))}
              disabled={!formData.province}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select your city" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px]">
                {formData.province && cities[formData.province as keyof typeof cities]
                  ? cities[formData.province as keyof typeof cities].map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))
                  : null}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Interests */}
        <div>
          <label className="block text-sm font-medium mb-2">Interested Program Types (Multiple Choice)*</label>
          <div className="space-y-2">
            {interestOptions.map((interest) => (
              <div key={interest} className="flex items-center">
                <Checkbox
                  id={`interest-${interest}`}
                  checked={formData.interests.includes(interest)}
                  onCheckedChange={() => handleMultiSelect("interests", interest)}
                />
                <label htmlFor={`interest-${interest}`} className="ml-2 text-sm">
                  {interest}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Preferred Destinations */}
        <div>
          <label className="block text-sm font-medium mb-2">Preferred Destinations (Multiple Choice)</label>
          <div className="space-y-2">
            {preferredDestinations.map((destination) => (
              <div key={destination} className="flex items-center">
                <Checkbox
                  id={`destination-${destination}`}
                  checked={formData.destinations.includes(destination)}
                  onCheckedChange={() => handleMultiSelect("destinations", destination)}
                />
                <label htmlFor={`destination-${destination}`} className="ml-2 text-sm">
                  {destination}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Learning Interests */}
        <div>
          <label className="block text-sm font-medium mb-2">I WANT TO LEARN ABOUT*</label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {learningInterests.map((interest) => (
              <div key={interest} className="flex items-center">
                <Checkbox
                  id={`learning-${interest}`}
                  checked={formData.learningInterests.includes(interest)}
                  onCheckedChange={() => handleMultiSelect("learningInterests", interest)}
                />
                <label htmlFor={`learning-${interest}`} className="ml-2 text-sm">
                  {interest}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium mb-2">
            Your Questions or Requirements
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleChange}
            rows={4}
            className="w-full p-3 border rounded-lg"
            placeholder="Please describe your specific needs or questions"
          />
        </div>

        {/* Consent Checkbox */}
        <div className="flex items-start gap-2">
          <Checkbox
            id="consent"
            checked={formData.consent}
            onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, consent: checked as boolean }))}
          />
          <label htmlFor="consent" className="text-sm text-gray-600">
            I agree to receive educational information and activity notifications from YOUNICKO. I understand that I can
            unsubscribe at any time.
          </label>
        </div>
      </div>

      <Button type="submit" size="lg" className="w-full md:w-auto">
        Submit
      </Button>
    </form>
  )
}
