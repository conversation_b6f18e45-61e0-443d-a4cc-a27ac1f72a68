"use client"

import { useState } from "react"
import { But<PERSON> } from "./ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface Video {
  id: string
  title: string
  url: string
}

const videos: Video[] = [
  { id: "1", title: "Featured Video 1", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
  { id: "2", title: "Featured Video 2", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
  { id: "3", title: "Featured Video 3", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
  { id: "4", title: "Featured Video 4", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
  { id: "5", title: "Featured Video 5", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
  { id: "6", title: "Featured Video 6", url: "https://www.youtube.com/embed/dQw4w9WgXcQ" },
]

const VIDEOS_PER_PAGE = 2

const VideoSection = () => {
  const [currentPage, setCurrentPage] = useState(1)

  const indexOfLastVideo = currentPage * VIDEOS_PER_PAGE
  const indexOfFirstVideo = indexOfLastVideo - VIDEOS_PER_PAGE
  const currentVideos = videos.slice(indexOfFirstVideo, indexOfLastVideo)

  const totalPages = Math.ceil(videos.length / VIDEOS_PER_PAGE)

  const nextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
  }

  const prevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1))
  }

  return (
    <section className="py-12 max-w-6xl mx-auto px-6">
      <h2 className="text-4xl font-bold mb-8 text-text-primary">Featured Videos</h2>
      <div className="grid md:grid-cols-2 gap-16">
        {currentVideos.map((video) => (
          <div key={video.id} className="relative w-full max-w-md mx-auto" style={{ paddingBottom: "42%" }}>
            <iframe
              src={video.url}
              title={video.title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="absolute top-0 left-0 w-full h-full rounded-lg"
            ></iframe>
          </div>
        ))}
      </div>
      <div className="flex justify-between items-center mt-8">
        <Button onClick={prevPage} disabled={currentPage === 1} variant="outline">
          <ChevronLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        <span className="text-text-secondary">
          Page {currentPage} of {totalPages}
        </span>
        <Button onClick={nextPage} disabled={currentPage === totalPages} variant="outline">
          Next <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </section>
  )
}

export default VideoSection
