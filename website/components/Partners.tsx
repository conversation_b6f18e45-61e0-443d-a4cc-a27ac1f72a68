"use client"

import Image from "next/image"

const partners = [
  {
    id: 1,
    name: "Harvard University",
    logo: "/placeholder.svg?height=60&width=160&text=Harvard",
  },
  {
    id: 2,
    name: "Stanford University",
    logo: "/placeholder.svg?height=60&width=160&text=Stanford",
  },
  {
    id: 3,
    name: "MIT",
    logo: "/placeholder.svg?height=60&width=160&text=MIT",
  },
  {
    id: 4,
    name: "Oxford University",
    logo: "/placeholder.svg?height=60&width=160&text=Oxford",
  },
  {
    id: 5,
    name: "Cambridge University",
    logo: "/placeholder.svg?height=60&width=160&text=Cambridge",
  },
  {
    id: 6,
    name: "National University of Singapore",
    logo: "/placeholder.svg?height=60&width=160&text=NUS",
  },
  {
    id: 7,
    name: "British Council",
    logo: "/placeholder.svg?height=60&width=160&text=British+Council",
  },
  {
    id: 8,
    name: "Education USA",
    logo: "/placeholder.svg?height=60&width=160&text=Education+USA",
  },
  {
    id: 9,
    name: "CIEE",
    logo: "/placeholder.svg?height=60&width=160&text=CIEE",
  },
  {
    id: 10,
    name: "IES Abroad",
    logo: "/placeholder.svg?height=60&width=160&text=IES+Abroad",
  },
  {
    id: 11,
    name: "University of Toronto",
    logo: "/placeholder.svg?height=60&width=160&text=Toronto",
  },
  {
    id: 12,
    name: "Yale University",
    logo: "/placeholder.svg?height=60&width=160&text=Yale",
  },
]

const Partners = () => {
  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 items-center">
          {partners.map((partner) => (
            <div
              key={partner.id}
              className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"
            >
              <Image
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                width={160}
                height={60}
                className="max-w-full h-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Partners
