"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface Review {
  id: number
  name: string
  avatar: string
  role: string
  comment: string
  rating: number
}

interface CustomerReviewsProps {
  programId: string
}

const CustomerReviews: React.FC<CustomerReviewsProps> = ({ programId }) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [reviews, setReviews] = useState<Review[]>([])
  const reviewsPerPage = 3

  useEffect(() => {
    // 这里模拟从API获取评论数据
    // 在实际应用中,您需要根据programId从后端获取相关的评论
    const fetchReviews = async () => {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500))

      const mockReviews: Review[] = [
        {
          id: 1,
          name: "<PERSON>",
          avatar: "https://i.pravatar.cc/150?img=1",
          role: "Student",
          comment: "This program exceeded my expectations. I learned so much about STEM and innovation!",
          rating: 5,
        },
        {
          id: 2,
          name: "<PERSON>",
          avatar: "https://i.pravatar.cc/150?img=2",
          role: "Parent",
          comment: "My son had an amazing experience at Berkeley. The program was well-organized and inspiring.",
          rating: 4,
        },
        {
          id: 3,
          name: "Sarah Williams",
          avatar: "https://i.pravatar.cc/150?img=3",
          role: "Teacher",
          comment: "As an educator, I'm impressed by the depth of the curriculum and the hands-on approach.",
          rating: 5,
        },
        {
          id: 4,
          name: "David Lee",
          avatar: "https://i.pravatar.cc/150?img=4",
          role: "Student",
          comment: "The program was challenging but incredibly rewarding. I made great friends and learned a lot!",
          rating: 4,
        },
        // Add more reviews as needed
      ]

      setReviews(mockReviews)
    }

    fetchReviews()
  }, [])

  const indexOfLastReview = currentPage * reviewsPerPage
  const indexOfFirstReview = indexOfLastReview - reviewsPerPage
  const currentReviews = reviews.slice(indexOfFirstReview, indexOfLastReview)
  const totalPages = Math.ceil(reviews.length / reviewsPerPage)

  return (
    <section className="py-12">
      <h2 className="text-3xl font-bold mb-8 text-center">Customer Reviews</h2>
      <div className="grid md:grid-cols-3 gap-8">
        {currentReviews.map((review) => (
          <div key={review.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <Image
                src={review.avatar || "/placeholder.svg"}
                alt={review.name}
                width={50}
                height={50}
                className="rounded-full mr-4"
              />
              <div>
                <h3 className="font-semibold">{review.name}</h3>
                <p className="text-sm text-gray-600">{review.role}</p>
              </div>
            </div>
            <p className="text-gray-700 mb-4">{review.comment}</p>
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-5 h-5 ${i < review.rating ? "text-yellow-400" : "text-gray-300"}`}
                  fill="currentColor"
                />
              ))}
            </div>
          </div>
        ))}
      </div>
      {totalPages > 1 && (
        <div className="flex justify-center mt-8 space-x-4">
          <Button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            variant="outline"
          >
            <ChevronLeft className="w-4 h-4 mr-2" /> Previous
          </Button>
          <Button
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            variant="outline"
          >
            Next <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      )}
    </section>
  )
}

export default CustomerReviews
