"use client"

import { useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"

const ChinaProgramSearch = () => {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [selectedGrades, setSelectedGrades] = useState<string[]>([])

  const programTypes = ["语言强化", "STEM与科学创新", "商务与艺术类", "学术拓展"]

  const cities = ["北京", "上海", "深圳", "成都", "西安"]

  const gradeOptions = ["小学", "初中", "高中"]

  const handleTypeChange = (type: string) => {
    setSelectedTypes((prev) => (prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]))
  }

  const handleCityChange = (city: string) => {
    setSelectedCities((prev) => (prev.includes(city) ? prev.filter((c) => c !== city) : [...prev, city]))
  }

  const handleGradeChange = (grade: string) => {
    setSelectedGrades((prev) => (prev.includes(grade) ? prev.filter((g) => g !== grade) : [...prev, grade]))
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <h2 className="text-lg font-bold mb-4">筛选项目</h2>

      {/* 项目类型 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">项目类型</h3>
        <div className="space-y-1.5">
          {programTypes.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${type}`}
                checked={selectedTypes.includes(type)}
                onCheckedChange={() => handleTypeChange(type)}
              />
              <label htmlFor={`type-${type}`} className="text-xs cursor-pointer">
                {type}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* 城市 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">城市</h3>
        <div className="space-y-1.5">
          {cities.map((city) => (
            <div key={city} className="flex items-center space-x-2">
              <Checkbox
                id={`city-${city}`}
                checked={selectedCities.includes(city)}
                onCheckedChange={() => handleCityChange(city)}
              />
              <label htmlFor={`city-${city}`} className="text-xs cursor-pointer">
                {city}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* 年级水平 */}
      <div className="mb-4">
        <h3 className="font-medium mb-2 text-sm">年级水平</h3>
        <div className="space-y-1.5">
          {gradeOptions.map((grade) => (
            <div key={grade} className="flex items-center space-x-2">
              <Checkbox
                id={`grade-${grade}`}
                checked={selectedGrades.includes(grade)}
                onCheckedChange={() => handleGradeChange(grade)}
              />
              <label htmlFor={`grade-${grade}`} className="text-xs cursor-pointer">
                {grade}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ChinaProgramSearch
