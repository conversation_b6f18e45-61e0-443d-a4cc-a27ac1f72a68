"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const faqs = [
  {
    question: "What types of programs do you offer?",
    answer:
      "We offer a wide range of educational programs including academic exchanges, cultural immersion experiences, language courses, summer camps, and specialized STEM programs. Our programs cater to different age groups from high school to university students.",
  },
  {
    question: "How long are your study abroad programs?",
    answer:
      "Program durations vary from short-term summer programs (2-8 weeks) to semester-long exchanges (3-4 months) and full academic year programs. We can help you choose the right duration based on your goals and schedule.",
  },
  {
    question: "What support services do you provide to students?",
    answer:
      "We provide comprehensive support including pre-departure orientation, visa application assistance, accommodation arrangements, 24/7 emergency support, local cultural guidance, and academic advising throughout your program.",
  },
  {
    question: "How much do programs typically cost?",
    answer:
      "Program costs vary depending on the destination, duration, and type of program. We offer transparent pricing that includes tuition, accommodation, and essential services. Financial aid and scholarship opportunities are available for eligible students.",
  },
  {
    question: "What are the accommodation options?",
    answer:
      "Accommodation options include homestays with local families, student dormitories, shared apartments, and campus housing. All options are carefully vetted to ensure safety and comfort.",
  },
  {
    question: "Do I need to know the local language?",
    answer:
      "Language requirements vary by program. While some programs require basic language proficiency, many offer language support or are conducted in English. We also provide language preparation resources before departure.",
  },
  {
    question: "What safety measures are in place?",
    answer:
      "Safety is our top priority. We maintain partnerships with reputable institutions, provide comprehensive insurance coverage, offer 24/7 emergency support, and regularly monitor local conditions in all program locations.",
  },
  {
    question: "Can I get academic credit for these programs?",
    answer:
      "Many of our programs offer academic credits that can be transferred to your home institution. We work with accredited educational partners and can provide necessary documentation for credit transfer.",
  },
  {
    question: "What is the application process?",
    answer:
      "The application process includes submitting an online application, academic records, and required documents. After review, selected candidates will have an interview. We provide step-by-step guidance throughout the process.",
  },
  {
    question: "Are there any scholarship opportunities?",
    answer:
      "Yes, we offer various scholarships based on academic merit, financial need, and specific program requirements. We also help students identify and apply for external funding opportunities.",
  },
]

const FAQSection = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  // Filter FAQs based on search term
  const filteredFAQs = faqs.filter(
    (faq) =>
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const toggleAccordion = (value: string) => {
    setExpandedItems((prev) => (prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]))
  }

  return (
    <div className="max-w-3xl mx-auto mb-16">
      <div className="mb-8">
        <Input
          type="text"
          placeholder="Search FAQs..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full border-[#00A0E9] focus-visible:ring-[#00A0E9]"
        />
      </div>
      <Accordion type="multiple" value={expandedItems} className="space-y-4">
        {filteredFAQs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger onClick={() => toggleAccordion(`item-${index}`)}>{faq.question}</AccordionTrigger>
            <AccordionContent>{faq.answer}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
      {filteredFAQs.length === 0 && (
        <p className="text-center text-muted-foreground mt-8">No matching questions found.</p>
      )}
    </div>
  )
}

export default FAQSection
