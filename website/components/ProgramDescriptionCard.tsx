"use client"

import type React from "react"

interface ProgramDescriptionCardProps {
  title: string
  description: string
}

const ProgramDescriptionCard: React.FC<ProgramDescriptionCardProps> = ({ title, description }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
      {/* 项目描述 */}
      <div>
        <h3 className="text-lg font-semibold mb-4">项目介绍</h3>
        <p className="text-gray-700 leading-relaxed">{description}</p>
      </div>

      {/* 导航面包屑 */}
      <div>
        <h4 className="text-md font-medium mb-3 text-gray-800">学习路径</h4>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">学</span>
          <span>→</span>
          <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full">观</span>
          <span>→</span>
          <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full">创</span>
          <span>→</span>
          <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full">思</span>
        </div>
      </div>
    </div>
  )
}

export default ProgramDescriptionCard
