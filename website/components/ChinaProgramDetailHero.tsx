import type React from "react"
import Image from "next/image"
import { MapPin } from "lucide-react"

interface ChinaProgramDetailHeroProps {
  title: string
  city: string
  backgroundImage: string
}

const ChinaProgramDetailHero: React.FC<ChinaProgramDetailHeroProps> = ({ title, city, backgroundImage }) => {
  return (
    <div className="relative h-[400px] w-full overflow-hidden">
      <Image
        src={backgroundImage || "/placeholder.svg"}
        alt={title}
        fill
        className="object-cover object-center"
        sizes="100vw"
      />
      <div className="absolute inset-0 bg-black/50" />
      <div className="relative z-10 h-full flex flex-col justify-center items-center text-center px-4 md:px-8 max-w-4xl mx-auto text-white">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">{title}</h1>
        <div className="flex items-center text-lg">
          <MapPin size={20} className="mr-2" />
          <span>中国,{city}</span>
        </div>
      </div>
    </div>
  )
}

export default ChinaProgramDetailHero
