"use client"

import { useTranslation } from "react-i18next"
import { Users, GraduationCap, Home, Clock, Globe, Award } from "lucide-react"

const WhyChooseUs = () => {
  const { t, ready } = useTranslation()

  const getText = (key: string, fallback: string) => {
    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  const features = [
    {
      icon: Users,
      titleKey: "whyChoose.leadership.title",
      titleFallback: "Experienced Leadership Team",
      descKey: "whyChoose.leadership.description",
      descFallback:
        "Our experienced team provides professional guidance and comprehensive support throughout your journey.",
    },
    {
      icon: GraduationCap,
      titleKey: "whyChoose.education.title",
      titleFallback: "High-Quality Education Programs",
      descKey: "whyChoose.education.description",
      descFallback:
        "Carefully designed programs with top partner institutions to provide excellent educational experiences.",
    },
    {
      icon: Home,
      titleKey: "whyChoose.accommodation.title",
      titleFallback: "Safe Accommodation and Healthy Dining",
      desc<PERSON>ey: "whyChoose.accommodation.description",
      descFallback: "Safe living environments and nutritious meals to ensure your health and well-being.",
    },
    {
      icon: Clock,
      titleKey: "whyChoose.support247.title",
      titleFallback: "24/7 Support",
      descKey: "whyChoose.support247.description",
      descFallback: "Round-the-clock support services to provide continuous assistance for students.",
    },
    {
      icon: Globe,
      titleKey: "whyChoose.cultural.title",
      titleFallback: "Immersive Cultural Experience",
      descKey: "whyChoose.cultural.description",
      descFallback: "Interactive activities and rich travel experiences to promote deep cultural learning.",
    },
    {
      icon: Award,
      titleKey: "whyChoose.academic.title",
      titleFallback: "Recognized Academic Excellence",
      descKey: "whyChoose.academic.description",
      descFallback: "Courses recognized by top universities, enhancing your academic credentials.",
    },
  ]

  return (
    <section className="relative w-screen py-16 -mx-4 md:-mx-6 lg:-mx-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-center"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&fit=crop')`,
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-black/40 via-black/20 to-black/40" />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full max-w-[80%] mx-auto px-6 md:px-8 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {getText("whyChoose.title", "Student Support and Safety")}
          </h2>
          <p className="text-xl text-white/90 max-w-4xl mx-auto">
            {getText(
              "whyChoose.subtitle",
              "We prioritize your health and safety first, providing comprehensive support services throughout your educational journey.",
            )}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-white mb-2">
                      {getText(feature.titleKey, feature.titleFallback)}
                    </h3>
                    <p className="text-white/90 text-sm leading-relaxed">
                      {getText(feature.descKey, feature.descFallback)}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default WhyChooseUs
