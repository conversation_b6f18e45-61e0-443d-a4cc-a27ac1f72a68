"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"

type ProgramStatus = "open" | "almost_full" | "over_capacity"

interface ProgramStatusCardProps {
  programId: string
  title: string
  date: string
  location: string
  remainingSpots: number
  status: ProgramStatus
  image: string
}

const statusColors: Record<ProgramStatus, string> = {
  open: "bg-[#B5B568]", // Using brand color
  almost_full: "bg-[#FFC107]", // Warning yellow
  over_capacity: "bg-[#F44336]", // Error red
}

const statusText: Record<ProgramStatus, string> = {
  open: "OPEN",
  almost_full: "ALMOST FULL",
  over_capacity: "OVER CAPACITY",
}

const ProgramStatusCard: React.FC<ProgramStatusCardProps> = ({
  programId,
  title,
  date,
  location,
  remainingSpots,
  status,
  image,
}) => {
  return (
    <Link
      href={`/programs/${programId}`}
      className="group bg-white rounded-lg shadow-md overflow-hidden flex flex-col transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg"
    >
      <div className="relative h-48">
        <Image
          src={image || "/placeholder.svg"}
          alt={title}
          fill
          className="object-cover transition-transform duration-200 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, 33vw"
        />
        <div
          className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium text-white ${statusColors[status]}`}
        >
          {statusText[status]}
        </div>
      </div>
      <div className="p-4 flex flex-col flex-grow">
        <h3 className="text-lg font-bold mb-2 text-text-primary line-clamp-2">{title}</h3>
        <p className="text-[#00A0E9] font-medium text-sm mb-1">{date}</p>
        <p className="text-text-secondary text-sm mb-1">{location}</p>
        <p className="text-text-secondary text-sm">{remainingSpots} spots remaining</p>
      </div>
    </Link>
  )
}

export default ProgramStatusCard
