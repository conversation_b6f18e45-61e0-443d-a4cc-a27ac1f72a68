import type React from "react"

interface YounikcoLogoProps {
  className?: string
}

const YounikcoLogo: React.FC<YounikcoLogoProps> = ({ className }) => {
  return (
    <svg className={className} viewBox="0 0 300 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0 10 L30 10 L15 70 L0 70 Z" fill="#FFD700" />
      <path d="M30 10 L60 10 L45 70 L30 70 Z" fill="#FFD700" />
      <path d="M60 10 L90 10 L75 70 L60 70 Z" fill="#87CEFA" />
      <path d="M90 10 L120 10 L105 70 L90 70 Z" fill="#87CEFA" />
      <path d="M120 10 L150 10 L135 70 L120 70 Z" fill="#87CEFA" />
      <path d="M150 10 L180 10 L165 70 L150 70 Z" fill="#FFD700" />
      <path d="M180 10 L210 10 L195 70 L180 70 Z" fill="#FFD700" />
      <path d="M210 10 L240 10 L225 70 L210 70 Z" fill="#FFD700" />

      <text x="10" y="95" fontSize="20" fill="#CD7F32">
        EXPLORE. LEARN. GROW
      </text>
    </svg>
  )
}

export default YounikcoLogo
