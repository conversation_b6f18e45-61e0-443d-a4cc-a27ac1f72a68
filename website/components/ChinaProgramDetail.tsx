"use client"

import type React from "react"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, GraduationCap } from 'lucide-react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Image from "next/image"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"

interface Program {
  programId: string
  title: string
  description: string
  type: string[]
  city: string
  gradeLevel: string[]
  duration: string
  sessions: string[]
  deadline: string
  images: string[]
}

interface ChinaProgramDetailProps {
  program: Program
}

const ChinaProgramDetail: React.FC<ChinaProgramDetailProps> = ({ program }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % program.images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + program.images.length) % program.images.length)
  }

  return (
    <div className="container mx-auto px-6 py-12 max-w-6xl">
      {/* 图片轮播 */}
      <div className="relative mb-8">
        <div className="relative h-[500px] rounded-xl overflow-hidden shadow-2xl">
          <Image
            src={program.images[currentImageIndex] || "/placeholder.svg"}
            alt={`${program.title} - 图片 ${currentImageIndex + 1}`}
            fill
            className="object-cover"
          />

          {program.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg"
              >
                <ChevronRight className="w-6 h-6" />
              </button>
            </>
          )}
        </div>

        {/* 图片指示器 */}
        {program.images.length > 1 && (
          <div className="flex justify-center mt-4 space-x-2">
            {program.images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-3 h-3 rounded-full ${index === currentImageIndex ? "bg-blue-600" : "bg-gray-300"}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* 项目信息标签 */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="space-y-6">
          {/* 第一行：项目类型 + 时长 */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-600">项目类型:</span>
              <div className="flex gap-2">
                {program.type.map((type, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {type}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">时长:</span>
              <span className="text-sm font-medium text-gray-900">{program.duration}</span>
            </div>
          </div>

          {/* 第二行：城市 + 年级 */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">城市:</span>
              <span className="text-sm font-medium text-gray-900">{program.city}</span>
            </div>

            <div className="flex items-center gap-2">
              <GraduationCap className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">年级:</span>
              <div className="flex gap-2">
                {program.gradeLevel.map((grade, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {grade}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* 第三行：营期 */}
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-600">营期:</span>
            <div className="flex flex-wrap gap-2">
              {program.sessions.map((session, index) => (
                <Badge key={index} variant="default" className="text-xs bg-blue-100 text-blue-800">
                  {session}
                </Badge>
              ))}
            </div>
          </div>

          {/* 第四行：截止日期 */}
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-red-600" />
            <span className="text-sm font-medium text-gray-600">截止日期:</span>
            <Badge variant="destructive" className="text-xs">
              {program.deadline}
            </Badge>
          </div>
        </div>
      </div>

      {/* 项目描述 */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h3 className="text-2xl font-bold mb-6 text-gray-900">项目介绍</h3>
        <p className="text-gray-700 leading-relaxed text-lg">{program.description}</p>
      </div>


      {/* 标签页内容 */}
      <div className="bg-white rounded-xl shadow-lg p-8">
        <Tabs defaultValue="highlights" className="w-full">
          <TabsList className="w-full flex justify-start border-b border-gray-200 bg-transparent h-auto p-0 rounded-none">
            <TabsTrigger
              value="highlights"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              亮点
            </TabsTrigger>
            <TabsTrigger
              value="academics"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              学术
            </TabsTrigger>
            <TabsTrigger
              value="itinerary"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              行程
            </TabsTrigger>
            <TabsTrigger
              value="admission"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              申请
            </TabsTrigger>
          </TabsList>

          <TabsContent value="highlights" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">项目亮点</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-3 mt-1">•</span>
                  <span className="text-gray-700 leading-relaxed">深度体验中国传统文化与现代创新的完美融合</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-3 mt-1">•</span>
                  <span className="text-gray-700 leading-relaxed">参访知名企业，了解中国数字经济发展模式</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-3 mt-1">•</span>
                  <span className="text-gray-700 leading-relaxed">与当地学生交流，提升跨文化沟通能力</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-3 mt-1">•</span>
                  <span className="text-gray-700 leading-relaxed">专业导师指导，获得学术认证</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-3 mt-1">•</span>
                  <span className="text-gray-700 leading-relaxed">沉浸式语言环境，快速提升中文水平</span>
                </li>
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="academics" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">学术内容</h3>
              <div className="space-y-3 text-gray-700">
                <p>本项目结合理论学习与实践体验，涵盖以下学术模块：</p>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">中国经济发展史与现状分析</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">数字化转型与创新案例研究</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">中华文化传承与现代化进程</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">跨文化商务沟通技巧</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">项目管理与团队协作</span>
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="itinerary" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">行程安排</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold">第一周：文化探索</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">成都市区文化景点参观</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">传统工艺体验工坊</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">当地大学校园参访</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">文化交流活动</span>
                    </li>
                  </ul>
                </div>
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold">第二周：创新实践</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">科技企业参访</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">创新项目实践</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">行业专家讲座</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">成果展示与总结</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="admission" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">申请要求</h3>
              <div className="space-y-3 text-gray-700">
                <div>
                  <h4 className="font-semibold mb-2">基本要求：</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">年龄：16-25岁</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">学历：高中在读或以上</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">语言：具备基础中文沟通能力</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">身体健康，适应集体生活</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">申请材料：</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">申请表</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">个人陈述</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">学术成绩单</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">推荐信（可选）</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">护照复印件</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default ChinaProgramDetail
