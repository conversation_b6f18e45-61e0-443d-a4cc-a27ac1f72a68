import type React from "react"

interface Option {
  value: string
  label: string
}

interface MultiSelectProps {
  label: string
  values: string[]
  onChange: (values: string[]) => void
  options: Option[]
}

export const MultiSelect: React.FC<MultiSelectProps> = ({ label, values, onChange, options }) => {
  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(event.target.selectedOptions, (option) => option.value)
    onChange(selectedOptions)
  }

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <select
        multiple
        value={values}
        onChange={handleChange}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange focus:border-orange sm:text-sm rounded-md"
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}
