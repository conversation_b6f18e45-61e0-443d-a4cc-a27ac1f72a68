"use client"

import * as React from "react"

import { cn } from "@/lib/utils"

const Progress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { value?: number; max?: number }
>(({ className, value, max, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("relative h-2 w-full overflow-hidden rounded-full bg-secondary", className)}
      {...props}
    >
      <div
        className="absolute left-0 top-0 h-2 bg-primary transition-all"
        style={{ width: `${((value || 0) / (max || 100)) * 100}%` }}
      />
    </div>
  )
})
Progress.displayName = "Progress"

export { Progress }
