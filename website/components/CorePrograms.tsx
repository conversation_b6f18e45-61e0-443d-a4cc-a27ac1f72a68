import Link from "next/link"
import { ArrowRight } from "lucide-react"
import Image from "next/image"

const programs = [
  {
    programId: "USART-2023-001",
    title: "Youth Art Advanced Program",
    description:
      "In collaboration with <PERSON> Audubon, perfectly combining art and nature to inspire students' creativity and imagination.",
    image: "https://images.unsplash.com/photo-1513364776144-60967b0f800f?q=80&w=2071&fit=crop",
    location: "USA",
    duration: "2 weeks",
  },
  {
    programId: "JPTE-2023-001",
    title: "Japan Tech Innovation Tour",
    description:
      "Explore Japan's advanced technology and experience the collision of cutting-edge tech with traditional culture.",
    image: "https://images.unsplash.com/photo-1536098561742-ca998e48cbcc?q=80&w=2036&fit=crop",
    location: "Japan",
    duration: "10 days",
  },
  {
    programId: "UKBA-2023-001",
    title: "UK Business Management Experience",
    description: "Gain deep insights into top UK business schools and develop an international business perspective.",
    image: "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?q=80&w=2069&fit=crop",
    location: "UK",
    duration: "3 weeks",
  },
]

const CorePrograms = () => {
  return (
    <section className="space-y-16">
      {/* Header Section */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="space-y-4">
          <p className="text-text-secondary text-lg">
            Our programs are carefully curated by world-class education experts to provide students with comprehensive
            learning experiences and personal growth opportunities.
          </p>
          <Link href="/programs" className="inline-flex items-center text-brand hover:text-brand/90 font-medium">
            View All Programs
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </div>
        <h2 className="text-3xl md:text-4xl font-bold text-text-primary">
          Redefining Study Abroad Experience, Creating Unique Learning Journey
        </h2>
      </div>

      {/* Program Cards */}
      <div className="grid md:grid-cols-3 gap-8">
        {programs.map((program) => (
          <Link key={program.programId} href={`/programs/${program.programId}`} className="group">
            <div className="bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg h-full flex flex-col">
              <div className="relative h-48">
                <Image
                  src={program.image || "/placeholder.svg"}
                  alt={program.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="p-6 flex flex-col flex-grow">
                <h3 className="text-xl font-semibold mb-2 text-text-primary line-clamp-2">{program.title}</h3>
                <p className="text-text-secondary mb-4 line-clamp-3">{program.description}</p>
                <div className="flex justify-between text-sm text-text-secondary mt-auto">
                  <span>{program.location}</span>
                  <span>{program.duration}</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </section>
  )
}

export default CorePrograms
