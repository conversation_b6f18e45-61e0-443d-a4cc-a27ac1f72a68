"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { User } from "lucide-react"

interface BlogPost {
  id: number
  title: string
  author: string
  date: string
  image: string
  category: string
  programType: string
  ageGroup: string
  region: string
  duration: string
}

const POSTS_PER_PAGE = 9

const BlogList = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [totalPosts, setTotalPosts] = useState(0)

  useEffect(() => {
    const fetchPosts = async () => {
      await new Promise((resolve) => setTimeout(resolve, 500))

      const mockPosts: BlogPost[] = [
        {
          id: 1,
          title: "我的新加坡之旅：阳光小岛的冒险！",
          author: "马克斯",
          date: "2023-06-15",
          image: "https://images.unsplash.com/photo-1525625293386-3f8f99389edd?q=80&w=2052&fit=crop",
          category: "Study Tips",
          programType: "Academic",
          ageGroup: "University",
          region: "Europe",
          duration: "Semester",
        },
        {
          id: 2,
          title: "我在爱普森学院的夏令营经历：16天的语言、文化与乐趣",
          author: "Ben",
          date: "2023-06-10",
          image: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=2070&fit=crop",
          category: "Cultural Exchange",
          programType: "Cultural",
          ageGroup: "High School",
          region: "Asia",
          duration: "Summer",
        },
        {
          id: 3,
          title: "我在立命馆大学难忘的语言交流项目",
          author: "Ray",
          date: "2023-06-05",
          image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?q=80&w=2070&fit=crop",
          category: "Language Exchange",
          programType: "Academic",
          ageGroup: "University",
          region: "Asia",
          duration: "Semester",
        },
        {
          id: 4,
          title: "英国文化探索：在牛津的学术之旅",
          author: "Emma",
          date: "2023-05-28",
          image: "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?q=80&w=2070&fit=crop",
          category: "Academic",
          programType: "Academic",
          ageGroup: "University",
          region: "Europe",
          duration: "Summer",
        },
        {
          id: 5,
          title: "美国西海岸科技创新营：硅谷的启发之旅",
          author: "Alex",
          date: "2023-05-20",
          image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=2070&fit=crop",
          category: "Technology",
          programType: "Academic",
          ageGroup: "High School",
          region: "North America",
          duration: "Summer",
        },
        {
          id: 6,
          title: "澳洲海洋生物研究项目：大堡礁的奇妙发现",
          author: "Sophie",
          date: "2023-05-15",
          image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?q=80&w=2070&fit=crop",
          category: "Research",
          programType: "Academic",
          ageGroup: "University",
          region: "Oceania",
          duration: "Semester",
        },
      ]

      setBlogPosts(mockPosts.slice((currentPage - 1) * POSTS_PER_PAGE, currentPage * POSTS_PER_PAGE))
      setTotalPosts(mockPosts.length)
    }

    fetchPosts()
  }, [currentPage])

  const totalPages = Math.ceil(totalPosts / POSTS_PER_PAGE)

  return (
    <section className="pt-6 pb-12 max-w-6xl mx-auto px-6">
      <h2 className="text-3xl font-bold mb-8 text-foreground leading-tight">最新文章</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {blogPosts.map((post) => (
          <Link key={post.id} href={`/blog/${post.id}`}>
            <article className="group relative overflow-hidden rounded-xl h-[240px] cursor-pointer transition-transform duration-300 hover:scale-105">
              {/* Background Image */}
              <div className="absolute inset-0">
                <Image
                  src={post.image || "/placeholder.svg"}
                  alt={post.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                {/* Dark Overlay */}
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors duration-300" />
              </div>

              {/* Content Overlay */}
              <div className="relative h-full flex flex-col justify-between p-4 text-white">
                {/* Title */}
                <h3 className="text-lg font-bold leading-tight line-clamp-3 mt-auto mb-4">{post.title}</h3>

                {/* Author */}
                <div className="flex items-center text-sm">
                  <User className="w-4 h-4 mr-2" />
                  <span>{post.author}</span>
                </div>
              </div>
            </article>
          </Link>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center gap-2">
          {Array.from({ length: totalPages }, (_, i) => (
            <Button
              key={i}
              onClick={() => setCurrentPage(i + 1)}
              variant={currentPage === i + 1 ? "default" : "outline"}
              className="min-w-[40px]"
            >
              {i + 1}
            </Button>
          ))}
        </div>
      )}
    </section>
  )
}

export default BlogList
