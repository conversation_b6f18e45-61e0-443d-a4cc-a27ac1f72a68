"use client"

import { useState } from "react"
import { Button } from "./ui/button"
import Image from "next/image"
import Link from "next/link"

type ActivityStatus = "normal" | "almost_full" | "over_capacity"

interface Activity {
  id: number
  title: string
  date: string
  location: string
  remainingSpots: number
  capacity: number
  status: ActivityStatus
  image: string
}

const statusColors: Record<ActivityStatus, string> = {
  normal: "bg-brand", // 更新为品牌色
  almost_full: "bg-[#FFC107]", // 黄色保持不变
  over_capacity: "bg-[#F44336]", // 红色保持不变
}

const statusText: Record<ActivityStatus, string> = {
  normal: "OPEN",
  almost_full: "ALMOST FULL",
  over_capacity: "OVER CAPACITY",
}

const activities: Activity[] = [
  {
    id: 1,
    title: "Cultural Exchange in Paris",
    date: "2023-07-15",
    location: "Paris, France",
    remainingSpots: 5,
    capacity: 20,
    status: "normal",
    image:
      "https://images.unsplash.com/photo-1502602898657-3e91760cbb34?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80",
  },
  {
    id: 2,
    title: "Tech Innovation Workshop",
    date: "2023-07-20",
    location: "Silicon Valley, USA",
    remainingSpots: 2,
    capacity: 15,
    status: "almost_full",
    image:
      "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
  },
  {
    id: 3,
    title: "Environmental Studies in Amazon",
    date: "2023-07-25",
    location: "Manaus, Brazil",
    remainingSpots: 0,
    capacity: 12,
    status: "over_capacity",
    image:
      "https://images.unsplash.com/photo-1516214104703-d870798883c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
  },
]

const RecentActivities = () => {
  const [filter, setFilter] = useState<"this_week" | "ending_soon" | "available">("this_week")

  return (
    <section className="grid gap-12">
      <div className="grid md:grid-cols-3 gap-8">
        {activities.map((activity) => (
          <Link
            key={activity.id}
            href={`/activities/${activity.id}`}
            className="group bg-white rounded-lg shadow-md overflow-hidden flex flex-col transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg"
          >
            <div className="relative h-48">
              <Image
                src={activity.image || "/placeholder.svg"}
                alt={activity.title}
                fill
                className="object-cover transition-transform duration-200 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
              <div
                className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium text-white ${statusColors[activity.status]}`}
              >
                {statusText[activity.status]}
              </div>
            </div>
            <div className="p-4 flex flex-col flex-grow">
              <h3 className="text-lg font-bold mb-2 text-text-primary line-clamp-2">{activity.title}</h3>
              <p className="text-orange font-medium text-sm mb-1">{activity.date}</p>
              <p className="text-text-secondary text-sm mb-1">{activity.location}</p>
              <p className="text-text-secondary text-sm">{activity.remainingSpots} spots remaining</p>
            </div>
          </Link>
        ))}
      </div>

      <div className="text-center">
        <Link href="/activities">
          <Button variant="default" size="lg" className="bg-[#B5B568] hover:bg-[#B5B568]/90 text-white">
            Know More Activities
          </Button>
        </Link>
      </div>
    </section>
  )
}

export default RecentActivities
