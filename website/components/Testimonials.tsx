"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import { useTranslation } from "react-i18next"
import { useContent } from "@/hooks/useContent"

const testimonialKeys = [
  {
    id: 1,
    contentKey: "testimonial_1_content",
    authorKey: "testimonial_1_author",
    roleKey: "testimonial_1_role",
    programKey: "testimonial_1_program",
    contentFallback:
      "我在新加坡的游学经历非常棒，不仅让我学到了很多科学知识，还让我体验了不同的文化。老师们都很专业，课程设计得很有趣，让我对科学产生了更大的兴趣。这次旅行真的改变了我对世界的看法。",
    authorFallback: "张文慧",
    roleFallback: "高中生",
    programFallback: "新加坡科学营",
    image:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80",
  },
  {
    id: 2,
    contentKey: "testimonial_2_content",
    authorKey: "testimonial_2_author",
    roleKey: "testimonial_2_role",
    programKey: "testimonial_2_program",
    contentFallback:
      "参加EdGoing的项目让我的孩子变得更加自信和独立。她不仅提高了英语水平，还学会了如何与来自不同文化背景的同学相处。这是一次非常值得的投资。",
    authorFallback: "李明",
    roleFallback: "学生家长",
    programFallback: "国际文化交流项目",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80",
  },
  {
    id: 3,
    contentKey: "testimonial_3_content",
    authorKey: "testimonial_3_author",
    roleKey: "testimonial_3_role",
    programKey: "testimonial_3_program",
    contentFallback:
      "通过EdGoing的STEM项目，我对编程和机器人技术产生了浓厚的兴趣。导师们都很专业，教学方式很有趣，让我在玩中学到了很多知识。",
    authorFallback: "王小明",
    roleFallback: "初中生",
    programFallback: "STEM创新营",
    image:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
  },
]

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const { t, ready } = useTranslation()
  const { getContent } = useContent()

  const getText = (key: string, fallback: string) => {
    // 优先从数据库获取内容，如果没有则使用 i18n
    const dbContent = getContent(key)
    if (dbContent) return dbContent

    if (!ready) return fallback
    try {
      return t(key) || fallback
    } catch {
      return fallback
    }
  }

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonialKeys.length)
  }

  const previousTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonialKeys.length) % testimonialKeys.length)
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {getText("testimonials_title", "学员故事")}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {getText("testimonials_subtitle", "听听我们学员分享他们的学习经历和成长故事。")}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Navigation Buttons */}
            <button
              onClick={previousTestimonial}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-12 text-gray-400 hover:text-gray-600 transition-colors z-10"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="w-8 h-8" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 text-gray-400 hover:text-gray-600 transition-colors z-10"
              aria-label="Next testimonial"
            >
              <ChevronRight className="w-8 h-8" />
            </button>

            {/* Testimonial Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="text-center space-y-8"
              >
                <div className="bg-gray-50 rounded-2xl p-8 md:p-12">
                  <p className="text-lg md:text-xl text-gray-700 italic leading-relaxed mb-8">
                    "{getText(testimonialKeys[currentIndex].contentKey, testimonialKeys[currentIndex].contentFallback)}"
                  </p>

                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-16 h-16 rounded-full overflow-hidden">
                      <Image
                        src={testimonialKeys[currentIndex].image || "/placeholder.svg"}
                        alt={getText(testimonialKeys[currentIndex].authorKey, testimonialKeys[currentIndex].authorFallback)}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-semibold text-gray-900 text-lg">
                        {getText(testimonialKeys[currentIndex].authorKey, testimonialKeys[currentIndex].authorFallback)}
                      </div>
                      <div className="text-gray-600">
                        {getText(testimonialKeys[currentIndex].roleKey, testimonialKeys[currentIndex].roleFallback)}
                      </div>
                      <div className="text-sm text-blue-600 font-medium">
                        {getText(testimonialKeys[currentIndex].programKey, testimonialKeys[currentIndex].programFallback)}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Pagination Dots */}
            <div className="flex justify-center space-x-2 mt-8">
              {testimonialKeys.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentIndex ? "bg-blue-600" : "bg-gray-300"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
