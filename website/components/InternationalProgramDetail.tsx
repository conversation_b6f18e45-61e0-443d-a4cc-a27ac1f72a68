"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, GraduationCap } from 'lucide-react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Image from "next/image"
import { useTranslation } from "react-i18next"

interface Program {
  programId: string
  titleKey: string
  titleFallback: string
  descriptionKey: string
  descriptionFallback: string
  typeKeys: string[]
  typeFallbacks: string[]
  countryKey: string
  countryFallback: string
  gradeLevelKeys: string[]
  gradeLevelFallbacks: string[]
  sessionKeys: string[]
  sessionFallbacks: string[]
  deadlineKey: string
  deadlineFallback: string
  durationKey: string
  durationFallback: string
  images: string[]
  highlightsKeys: string[]
  highlightsFallbacks: string[]
  academicsKeys: string[]
  academicsFallbacks: string[]
  itineraryKeys: string[]
  itineraryFallbacks: string[]
  admissionKeys: string[]
  admissionFallbacks: string[]
}

interface InternationalProgramDetailProps {
  program: Program
}

const InternationalProgramDetail: React.FC<InternationalProgramDetailProps> = ({ program }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const { t, ready } = useTranslation()

  const getText = (key: string, fallback: string) => {
    if (!ready) return fallback
    try {
      const translated = t(key)
      return translated && translated !== key ? translated : fallback
    } catch {
      return fallback
    }
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % program.images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + program.images.length) % program.images.length)
  }

  return (
    <div className="container mx-auto px-6 py-12 max-w-6xl">
      {/* 图片轮播 */}
      <div className="relative mb-8">
        <div className="relative h-[500px] overflow-hidden">
          <Image
            src={program.images[currentImageIndex] || "/placeholder.svg"}
            alt={`${getText(program.titleKey, program.titleFallback)} - ${getText("common.image", "图片")} ${currentImageIndex + 1}`}
            fill
            className="object-cover"
          />

          {program.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg"
              >
                <ChevronRight className="w-6 h-6" />
              </button>
            </>
          )}
        </div>

        {/* 图片指示器 */}
        {program.images.length > 1 && (
          <div className="flex justify-center mt-4 space-x-2">
            {program.images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-3 h-3 rounded-full ${index === currentImageIndex ? "bg-blue-600" : "bg-gray-300"}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* 项目信息标签 - 按照目标图片布局 */}
      <div className="bg-white p-8 mb-8">
        <div className="space-y-6">
          {/* 第一行：项目类型 + 时长 */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-600">{getText("program.projectType", "项目类型")}:</span>
              <div className="flex gap-2">
                {program.typeKeys.map((typeKey, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {getText(typeKey, program.typeFallbacks[index])}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">{getText("program.duration", "时长")}:</span>
              <span className="text-sm font-medium text-gray-900">
                {getText(program.durationKey, program.durationFallback)}
              </span>
            </div>
          </div>

          {/* 第二行：城市 + 年级 */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">{getText("program.city", "城市")}:</span>
              <span className="text-sm font-medium text-gray-900">
                {getText(program.countryKey, program.countryFallback)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <GraduationCap className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">{getText("program.gradeLevel", "年级")}:</span>
              <div className="flex gap-2">
                {program.gradeLevelKeys.map((gradeKey, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {getText(gradeKey, program.gradeLevelFallbacks[index])}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* 第二行：营期 */}
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-600">{getText("program.sessions", "营期")}:</span>
            <div className="flex flex-wrap gap-2">
              {program.sessionKeys.map((sessionKey, index) => (
                <Badge key={index} variant="default" className="text-xs bg-blue-100 text-blue-800">
                  {getText(sessionKey, program.sessionFallbacks[index])}
                </Badge>
              ))}
            </div>
          </div>

          {/* 第三行：截止日期 */}
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-red-600" />
            <span className="text-sm font-medium text-gray-600">{getText("program.deadline", "截止日期")}:</span>
            <Badge variant="destructive" className="text-xs">
              {getText(program.deadlineKey, program.deadlineFallback)}
            </Badge>
          </div>
        </div>
      </div>

      {/* 项目描述 */}
      <div className="bg-white p-8 mb-8">
        <h3 className="text-2xl font-bold mb-6 text-gray-900">项目介绍</h3>
        <p className="text-gray-700 leading-relaxed text-lg">
          {getText(program.descriptionKey, program.descriptionFallback)}
        </p>
      </div>

      {/* 标签页内容 */}
      <div className="bg-white p-8">
        <Tabs defaultValue="highlights" className="w-full">
          <TabsList className="w-full flex justify-start border-b border-gray-200 bg-transparent h-auto p-0 rounded-none">
            <TabsTrigger
              value="highlights"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              {getText("tabs.highlights", "项目亮点")}
            </TabsTrigger>
            <TabsTrigger
              value="features"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              {getText("tabs.features", "项目特色")}
            </TabsTrigger>
            <TabsTrigger
              value="itinerary"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              {getText("tabs.itinerary", "行程安排")}
            </TabsTrigger>
            <TabsTrigger
              value="additional"
              className="data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:bg-transparent text-gray-600 hover:text-gray-800 rounded-none px-6 py-3 border-b-2 border-transparent font-medium"
            >
              {getText("tabs.additional", "额外信息")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="highlights" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {getText("sections.projectHighlights", "项目亮点")}
              </h3>
              <ul className="space-y-3">
                {program.highlightsKeys.map((highlightKey, index) => (
                  <li className="flex items-start" key={index}>
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span className="text-gray-700 leading-relaxed">
                      {getText(highlightKey, program.highlightsFallbacks[index])}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="features" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {getText("sections.projectFeatures", "项目特色")}
              </h3>
              <div className="space-y-3 text-gray-700">
                <p>{getText("features.intro", "本项目具有以下独特特色：")}</p>
                <ul className="space-y-3">
                  {program.academicsKeys.map((academicKey, index) => (
                    <li className="flex items-start" key={index}>
                      <span className="text-blue-500 mr-3 mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed">
                        {getText(academicKey, program.academicsFallbacks[index])}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="itinerary" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">{getText("sections.itinerary", "行程安排")}</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold">{getText("itinerary.week1", "第一周：基础学习")}</h4>
                  <ul className="space-y-3">
                    {program.itineraryKeys
                      .slice(0, Math.ceil(program.itineraryKeys.length / 2))
                      .map((itineraryKey, index) => (
                        <li className="flex items-start" key={index}>
                          <span className="text-blue-500 mr-3 mt-1">•</span>
                          <span className="text-gray-700 leading-relaxed">
                            {getText(itineraryKey, program.itineraryFallbacks[index])}
                          </span>
                        </li>
                      ))}
                  </ul>
                </div>
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold">{getText("itinerary.week2", "第二周：深度实践")}</h4>
                  <ul className="space-y-3">
                    {program.itineraryKeys
                      .slice(Math.ceil(program.itineraryKeys.length / 2))
                      .map((itineraryKey, index) => {
                        const actualIndex = Math.ceil(program.itineraryKeys.length / 2) + index
                        return (
                          <li className="flex items-start" key={actualIndex}>
                            <span className="text-blue-500 mr-3 mt-1">•</span>
                            <span className="text-gray-700 leading-relaxed">
                              {getText(itineraryKey, program.itineraryFallbacks[actualIndex])}
                            </span>
                          </li>
                        )
                      })}
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="additional" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {getText("sections.additionalInfo", "额外信息")}
              </h3>
              <div className="space-y-3 text-gray-700">
                <div>
                  <h4 className="font-semibold mb-2">{getText("additional.requirements", "申请要求")}：</h4>
                  <ul className="space-y-3">
                    {program.admissionKeys.slice(0, 4).map((admissionKey, index) => (
                      <li className="flex items-start" key={index}>
                        <span className="text-blue-500 mr-3 mt-1">•</span>
                        <span className="text-gray-700 leading-relaxed">
                          {getText(admissionKey, program.admissionFallbacks[index])}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">{getText("additional.materials", "申请材料")}：</h4>
                  <ul className="space-y-3">
                    {program.admissionKeys.slice(4).map((admissionKey, index) => {
                      const actualIndex = 4 + index
                      return (
                        <li className="flex items-start" key={actualIndex}>
                          <span className="text-blue-500 mr-3 mt-1">•</span>
                          <span className="text-gray-700 leading-relaxed">
                            {getText(admissionKey, program.admissionFallbacks[actualIndex])}
                          </span>
                        </li>
                      )
                    })}
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default InternationalProgramDetail
