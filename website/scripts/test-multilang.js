const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testMultiLang() {
  try {
    console.log('Testing multi-language settings...')
    
    // Create a test setting in Chinese
    const zhSetting = await prisma.setting.create({
      data: {
        key: 'test_title',
        value: '测试标题',
        type: 'TEXT',
        language: 'zh'
      }
    })
    console.log('Created Chinese setting:', zhSetting)
    
    // Create the same setting in English
    const enSetting = await prisma.setting.create({
      data: {
        key: 'test_title',
        value: 'Test Title',
        type: 'TEXT',
        language: 'en'
      }
    })
    console.log('Created English setting:', enSetting)
    
    // Fetch all settings
    const allSettings = await prisma.setting.findMany()
    console.log('All settings:', allSettings)
    
    // Clean up test data
    await prisma.setting.deleteMany({
      where: {
        key: 'test_title'
      }
    })
    console.log('Test completed successfully!')
    
  } catch (error) {
    console.error('Error testing multi-language:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testMultiLang()
