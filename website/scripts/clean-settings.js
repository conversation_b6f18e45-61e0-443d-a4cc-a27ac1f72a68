const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanSettings() {
  try {
    console.log('Cleaning existing settings...')
    
    // Delete all existing settings
    await prisma.setting.deleteMany({})
    
    console.log('Settings cleaned successfully!')
  } catch (error) {
    console.error('Error cleaning settings:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanSettings()
